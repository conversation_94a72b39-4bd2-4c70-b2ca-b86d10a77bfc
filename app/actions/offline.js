import _ from 'lodash';
import { Alert } from 'react-native';
import Meteor from 'react-native-meteor';
import * as environmentSettings from '../shared/Settings';
import Random from '../shared/Random';
import moment from 'moment-timezone';
import Orgs from '../api/Orgs';
import AsyncStorage from '@react-native-async-storage/async-storage';

import {
  OFFLINE_ADD_SLEEP_MOMENTS,
  OFFLINE_SLEEP_CHECK,
  OFFLINE_END_SLEEP,
  OFFLINE_NEW_MOMENT,
  OFFLINE_SYNC_MOMENTS,
  OFFLINE_START_SYNC_MOMENTS,
  AUTH_CLEAR_DATA,
  OFFLINE_CHECKIN,
  OFFLINE_CHECKIN_SUCCESS,
  OFFLINE_CHECKIN_ERROR,
  OFFLINE_UPDATE_PEOPLE, OFFLINE_CLEAR_MOMENTS,
} from '../utility/actions';

import { setOptions, fetchAndParse } from '../utility/httpFetch';

export const addSleepMoments = (taggedPeople = []) => {
  return (dispatch, getState) => {
    const { offlinePeople, offlineGroups } = getState().auth;
    const newMoments = [];
    for (const id of taggedPeople) {
      const person = _.find(offlinePeople, {_id: id});
      const now = new moment();
      if (person) {
        newMoments.push({
          _id: Random.id(),
          firstName: person.firstName,
          lastName: person.lastName,
          personId: person._id,
          createdAt: now.valueOf(),
          sleepCheckInterval: null,
          offlineType: 'sleep',
          time: now.format("h:mm a"),
          date: now.format("MM/DD/YYYY"),
          momentType: "sleep",
        })
      }
    }
    dispatch({ type: OFFLINE_ADD_SLEEP_MOMENTS, payload: { newMoments } });
  }
};

export const sleepCheck = (_id, opts = {}) => {
  return (dispatch, getState) => {
    const { sleepMoments } = getState().offline;
    const { personInfo } = getState().auth;
    const sleepMoment = _.find(sleepMoments, { _id });
    if (!sleepMoment) return;

    const createdAt = new Date().valueOf();
    const sleepCheck = {
      personId: sleepMoment.personId,
      createdAt,
      createdBy: personInfo?._id ?? null,
    }

    if (opts.sleepPosition) {
      sleepCheck.sleepPosition = opts.sleepPosition
    }

    if (opts.distressedSleep) {
      sleepCheck.distressedSleep = opts.distressedSleep
    }
    const sleepChecks = sleepMoment?.sleepChecks ?? [];
    sleepChecks.push(sleepCheck);
    sleepMoment.sleepLastSleepCheck = createdAt
    sleepMoment.sleepChecks = sleepChecks;

    dispatch({ type: OFFLINE_SLEEP_CHECK, payload: { sleepMoment }});
  }
}

export const endSleep = (_id, opts = {}) => {
  return (dispatch, getState) => {
    const { sleepMoments } = getState().offline;
    const { personInfo } = getState().auth;
    const sleepMoment = _.find(sleepMoments, { _id });
    if (!sleepMoment) return;
    const newSleepMoment = {...sleepMoment, ...opts};
    dispatch({ type: OFFLINE_END_SLEEP, payload: { sleepMoment: newSleepMoment }});
  }
}

export const nameToFace = (taggedPeople) => {
  return (dispatch, getState) => {
    const now = new moment();
    const { personInfo, orgInfo } = getState().auth;
    const { cachedPeople } = getState().offline;

    console.log('nameToFace action received taggedPeople:', JSON.stringify(taggedPeople, null, 2));

    // Create a description of the confirmed people
    let momentDescription = "";

    // Extract IDs from taggedPeople if it contains objects
    let personIds = [];
    if (taggedPeople && taggedPeople.length > 0) {
      if (typeof taggedPeople[0] === 'object') {
        if (taggedPeople[0].tagId) {
          // Format from the tagger component: [{tagId: "123", tagLabel: "Name"}]
          personIds = taggedPeople.map(p => p.tagId);

          // Create description directly from the tagLabel
          taggedPeople.forEach(person => {
            momentDescription += `Confirmed ${person.tagLabel}\n`;
          });

          console.log('Extracted tagIds from taggedPeople:', personIds);
        } else if (taggedPeople[0]._id) {
          // Format with full person objects: [{_id: "123", firstName: "John", ...}]
          personIds = taggedPeople.map(p => p._id);

          // Create description from the person objects
          taggedPeople.forEach(person => {
            momentDescription += `Confirmed ${person.firstName} ${person.lastName}\n`;
          });

          console.log('Extracted _ids from taggedPeople:', personIds);
        }
      } else {
        // taggedPeople is already an array of IDs
        personIds = taggedPeople;

        // Get the names of the tagged people for the description
        if (cachedPeople && cachedPeople.length > 0) {
          const taggedPeopleDetails = cachedPeople.filter(person =>
            personIds.includes(person._id)
          );

          taggedPeopleDetails.forEach(person => {
            momentDescription += `Confirmed ${person.firstName} ${person.lastName}\n`;
          });
        }
      }
    }

    // Store the offline data needed for syncing
    const offlineData = {
      selectedPeople: personIds,
      completedById: personInfo?._id,
      filterGroupId: personInfo?.checkInGroupId || personInfo?.defaultGroupId,
      inComplete: false,
      moment_type: "Name to Face"
    };

    const newMoment = {
      offlineType: 'nameToFace',
      momentType: 'nameToFace',
      momentTypePretty: 'Name To Face',
      taggedPeople,
      createdAt: now.valueOf(),
      sortStamp: now.valueOf(),
      date: now.format("MM/DD/YYYY"),
      time: now.format("h:mm a"),
      prettyTime: now.format("h:mm a"),
      currentPerson: personInfo,
      currentOrg: orgInfo,
      nameToFaceGroupId: personInfo?.checkInGroupId || personInfo?.defaultGroupId,
      nameToFaceClassroomGroupId: personInfo?.checkInGroupId || personInfo?.defaultGroupId,
      owner: personInfo?._id,
      comment: `${taggedPeople.length}/${taggedPeople.length} confirmed.\n`,
      nameToFaceDescription: momentDescription,
      offlineData
    }

    console.log('Creating offline nameToFace moment:', newMoment);
    dispatch({ type: OFFLINE_NEW_MOMENT, payload: { newMoment }});
  };
}

export const saveMoment = (name, data) => {
  return (dispatch, getState) => {
    const newMoment = {
      personId: data.personId,
      offlineType: name,
      momentType: name,
      taggedPeople: data.taggedPeople,
      date: moment(data.date).format("MM/DD/YYYY"),
      time: moment(data.time).format("h:mm a"),
      prettyTime: moment(data.time).format("h:mm a"),
      momentFieldValues: data.momentFieldValues,
      createdAt: data.createdAt,
      currentUser: getState().auth.userInfo,
      currentPerson: getState().auth.personInfo,
      currentOrg: data.currentOrg,
      attachedMedia: data.attachedMedia,
      comment: data.momentFieldValues?.comment,
    };

    if (name === 'checkOut') {
      newMoment.mood = data.momentFieldValues?.mood;
      newMoment.checkedOutById = data.momentFieldValues?.checkedOutById;

      if (data.currentOrg && data.currentOrg._id) {
        newMoment.orgId = data.currentOrg._id;
      } else if (getState().auth.orgInfo && getState().auth.orgInfo._id) {
        newMoment.orgId = getState().auth.orgInfo._id;
      }
    } else if (name === 'checkIn') {
      newMoment.checkInTime = data.timestamp || data.checkInTime;
      newMoment.checkInGroupId = data.groupId;
      newMoment.checkInGroupName = data.groupName;

      if (data.currentOrg && data.currentOrg._id) {
        newMoment.orgId = data.currentOrg._id;
      } else if (getState().auth.orgInfo && getState().auth.orgInfo._id) {
        newMoment.orgId = getState().auth.orgInfo._id;
      }

      // If we have selectedChildren, use them as taggedPeople
      if (data.selectedChildren && data.selectedChildren.length > 0) {
        newMoment.taggedPeople = data.selectedChildren;
      }
    }

    console.log(`Saving ${name} moment to Redux store:`, newMoment);
    dispatch({ type: OFFLINE_NEW_MOMENT, payload: { newMoment }});
  }
}

export const syncMoments = () => {
  return async (dispatch, getState) => {
    console.log("Starting moment sync");
    dispatch({ type: OFFLINE_START_SYNC_MOMENTS, payload: { momentsSyncing: true } });
    const { resumeToken } = getState().auth;
    const { momentsToSync } = getState().offline;
    if (!resumeToken) {
      Alert.alert("Cannot Sync - please go online to reset your authentication token ");
      return;
    }

    // Get any stored checkout data from AsyncStorage
    let storedCheckouts = [];
    try {
      const checkoutsData = await AsyncStorage.getItem('offline_checkouts');
      if (checkoutsData) {
        storedCheckouts = JSON.parse(checkoutsData);
        console.log(`Found ${storedCheckouts.length} stored checkouts in AsyncStorage`);
      }
    } catch (error) {
      console.error('Error retrieving stored checkouts:', error);
    }

    // Get any stored check-in data from AsyncStorage
    let storedCheckins = [];
    try {
      const checkinsData = await AsyncStorage.getItem('offline_checkins');
      if (checkinsData) {
        storedCheckins = JSON.parse(checkinsData);
        console.log(`Found ${storedCheckins.length} stored check-ins in AsyncStorage`);
      }
    } catch (error) {
      console.error('Error retrieving stored check-ins:', error);
    }

    // Filter out checkout moments to process separately
    const checkoutMoments = momentsToSync.filter(moment => moment.offlineType === 'checkOut');
    const checkinMoments = momentsToSync.filter(moment => moment.offlineType === 'checkIn');
    const nameToFaceMoments = momentsToSync.filter(moment => moment.offlineType === 'nameToFace');
    const otherMoments = momentsToSync.filter(moment =>
      moment.offlineType !== 'checkOut' &&
      moment.offlineType !== 'checkIn' &&
      moment.offlineType !== 'nameToFace');

    console.log(`Found ${checkoutMoments.length} checkout moments, ${checkinMoments.length} checkin moments, ${nameToFaceMoments.length} nameToFace moments, and ${otherMoments.length} other moments to sync`);

    let checkoutPromises = [];
    let checkinPromises = [];
    let nameToFacePromises = [];
    let otherMomentsPromises = [];

    // Process checkout moments from Redux store
    let checkoutErrors = 0;
    checkoutPromises = checkoutMoments.map(moment => {
      return new Promise((resolve) => {
        console.log('Syncing check out moment from Redux', JSON.stringify({
          offlineType: moment.offlineType,
          personId: moment.personId,
          prettyTime: moment.prettyTime,
          comments: moment.comments,
          mood: moment.mood,
          checkedOutById: moment.checkedOutById,
          orgId: moment.orgId,
        }));

        if (!moment.orgId && moment.currentOrg && moment.currentOrg._id) {
          moment.orgId = moment.currentOrg._id;
          console.log("Added orgId from currentOrg:", moment.orgId);
        }

        Meteor.call('checkOut', moment, (error, result) => {
          if (error) {
            console.log('Error syncing checkout moment: ', error);
            console.log('Error details:', error.details);
            console.log('Error reason:', error.reason);
            console.log('Checkout moment data: ', JSON.stringify(moment));
            checkoutErrors++;
            resolve(false);
          } else {
            console.log('Checkout moment synced successfully');
            resolve(true);
          }
        });
      });
    });

    // Process checkout moments from AsyncStorage
    const storedCheckoutPromises = storedCheckouts.map(checkout => {
      return new Promise((resolve) => {
        // Create a moment object from the stored checkout data
        const moment = {
          personId: checkout._id,
          offlineType: 'checkOut',
          momentType: 'checkOut',
          checkOutTime: checkout.checkOutTime,
          createdAt: checkout.checkOutTime,
          ...checkout.checkOutData
        };

        console.log('Syncing check out moment from AsyncStorage', JSON.stringify({
          personId: moment.personId,
          checkOutTime: moment.checkOutTime,
        }));

        Meteor.call('checkOut', moment, (error, result) => {
          if (error) {
            console.log('Error syncing stored checkout moment: ', error);
            console.log('Error details:', error.details);
            console.log('Error reason:', error.reason);
            checkoutErrors++;
            resolve(false);
          } else {
            console.log('Stored checkout moment synced successfully');
            resolve(true);
          }
        });
      });
    });

    // Process check-in moments from Redux store
    let checkinErrors = 0;
    checkinPromises = checkinMoments.map(moment => {
      return new Promise((resolve) => {
        console.log('Syncing check in moment from Redux', JSON.stringify({
          offlineType: moment.offlineType,
          personId: moment.personId,
          prettyTime: moment.prettyTime,
          orgId: moment.orgId,
        }));

        if (!moment.orgId && moment.currentOrg && moment.currentOrg._id) {
          moment.orgId = moment.currentOrg._id;
          console.log("Added orgId from currentOrg:", moment.orgId);
        }

        Meteor.call('checkIn', moment, (error, result) => {
          if (error) {
            console.log('Error syncing checkin moment: ', error);
            console.log('Error details:', error.details);
            console.log('Error reason:', error.reason);
            console.log('Checkin moment data: ', JSON.stringify(moment));
            checkinErrors++;
            resolve(false);
          } else {
            console.log('Checkin moment synced successfully');
            resolve(true);
          }
        });
      });
    });

    // Process check-in moments from AsyncStorage
    const storedCheckinPromises = storedCheckins.map(checkin => {
      return new Promise((resolve) => {
        // Create a moment object from the stored check-in data
        const moment = {
          personId: checkin._id,
          offlineType: 'checkIn',
          momentType: 'checkIn',
          checkInTime: checkin.checkInTime,
          createdAt: checkin.checkInTime,
          checkInGroupId: checkin.checkInGroupId,
          checkInGroupName: checkin.checkInGroupName
        };

        console.log('Syncing check in moment from AsyncStorage', JSON.stringify({
          personId: moment.personId,
          checkInTime: moment.checkInTime,
          checkInGroupId: moment.checkInGroupId
        }));

        Meteor.call('checkIn', moment, (error, result) => {
          if (error) {
            // If the person is already checked in, consider it a success
            if (error.reason === "Person already checked in") {
              console.log('Person already checked in on server:', moment.personId);
              resolve(true);
              return;
            }

            console.log('Error syncing stored checkin moment: ', error);
            console.log('Error details:', error.details);
            console.log('Error reason:', error.reason);
            checkinErrors++;
            resolve(false);
          } else {
            console.log('Stored checkin moment synced successfully');
            resolve(true);
          }
        });
      });
    });

    // Combine all checkout and checkin promises
    checkoutPromises = [...checkoutPromises, ...storedCheckoutPromises];
    checkinPromises = [...checkinPromises, ...storedCheckinPromises];

    // First process all check-ins
    await Promise.all(checkinPromises).then(async (results) => {
      if (checkinErrors > 0) {
        console.log(`${checkinErrors} check-in moments failed to sync`);
      } else if (storedCheckins.length > 0) {
        // Clear the AsyncStorage if all check-ins were synced successfully
        try {
          await AsyncStorage.setItem('offline_checkins', JSON.stringify([]));
          console.log('Cleared stored check-ins from AsyncStorage');
        } catch (error) {
          console.error('Error clearing stored check-ins:', error);
        }
      }
    });

    // Then process all check-outs
    await Promise.all(checkoutPromises).then(async (results) => {
      if (checkoutErrors > 0) {
        console.log(`${checkoutErrors} checkout moments failed to sync`);
      } else if (storedCheckouts.length > 0) {
        // Clear the AsyncStorage if all checkouts were synced successfully
        try {
          await AsyncStorage.setItem('offline_checkouts', JSON.stringify([]));
          console.log('Cleared stored checkouts from AsyncStorage');
        } catch (error) {
          console.error('Error clearing stored checkouts:', error);
        }
      }
    });

    // Process name to face moments
    let nameToFaceErrors = 0;
    if (nameToFaceMoments.length > 0) {
      console.log(`Processing ${nameToFaceMoments.length} name to face moments`);
      nameToFacePromises = nameToFaceMoments.map(moment => {
        return new Promise((resolve) => {
          console.log('Syncing name to face moment:', JSON.stringify({
            offlineType: moment.offlineType,
            taggedPeople: moment.taggedPeople,
            nameToFaceGroupId: moment.nameToFaceGroupId,
            createdAt: moment.createdAt,
            date: moment.date,
            time: moment.time
          }));

          // Use nameToFaceCheckConfirmation method for name to face moments
          // Get the stored data from the moment
          let nameToFaceParams = {};

          // If we have offlineData, use it directly
          if (moment.offlineData) {
            nameToFaceParams = { ...moment.offlineData };
            console.log('Using stored offlineData for nameToFace sync:', nameToFaceParams);
          } else {
            // Otherwise build the parameters from the moment
            let selectedPeople = moment.taggedPeople || [];

            // Convert objects with tagId/tagLabel to simple IDs if needed
            if (selectedPeople.length > 0 && typeof selectedPeople[0] === 'object') {
              if (selectedPeople[0].tagId) {
                // Format from the tagger component: [{tagId: "123", tagLabel: "Name"}]
                selectedPeople = selectedPeople.map(p => p.tagId);
                console.log('Converted tagId objects to IDs:', selectedPeople);
              } else if (selectedPeople[0]._id) {
                // Format with full person objects: [{_id: "123", firstName: "John", ...}]
                selectedPeople = selectedPeople.map(p => p._id);
                console.log('Converted person objects to IDs:', selectedPeople);
              }
            }

            nameToFaceParams = {
              selectedPeople: selectedPeople,
              completedById: moment.owner,
              filterGroupId: moment.nameToFaceGroupId,
              inComplete: false,
              moment_type: moment.momentTypePretty || "Name to Face"
            };

            console.log('Built nameToFaceParams from moment:', nameToFaceParams);
          }

          console.log('Calling nameToFaceCheckConfirmation with params:', JSON.stringify(nameToFaceParams, null, 2));

          Meteor.call('nameToFaceCheckConfirmation', nameToFaceParams, (error, result) => {
            if (error) {
              console.log('Error syncing name to face moment: ', error);
              console.log('Error details:', error.details);
              console.log('Error reason:', error.reason);
              nameToFaceErrors++;
              resolve(false);
            } else {
              console.log('Name to face moment synced successfully');
              resolve(true);
            }
          });
        });
      });

      await Promise.all(nameToFacePromises).then((results) => {
        if (nameToFaceErrors > 0) {
          console.log(`${nameToFaceErrors} name to face moments failed to sync`);
        } else {
          console.log('All name to face moments synced successfully');
        }
      });
    }

    // Finally process other moments
    if (otherMoments.length > 0) {
      otherMoments.forEach(moment => {
        console.log(`Syncing ${moment.momentType} moment for ${moment.taggedPeople.length} at ${moment.time} on ${moment.date}`);
        otherMomentsPromises.push(
          new Promise((resolve) => {
            Meteor.call('validateMoment', moment, (error, result) => {
              if (error) {
                console.log('Error syncing moment: ', error);
                console.log('Error details:', error.details);
                console.log('Error reason:', error.reason);
                resolve(false);
              } else {
                Meteor.call('insertMoment', moment, (error, result) => {
                  if (error) {
                    console.log('Error inserting moment: ', error);
                    console.log('Error details:', error.details);
                    console.log('Error reason:', error.reason);
                    resolve(false);
                  } else {
                    console.log('Moment inserted successfully');
                    resolve(true);
                  }
                });
              }
            });
          })
        );
      });

      await Promise.all(otherMomentsPromises).then(() => {
        console.log('All other moments synced successfully');
      });
    }

    // Mark sync as complete
    dispatch({ type: OFFLINE_SYNC_MOMENTS });
    dispatch({ type: OFFLINE_CLEAR_MOMENTS });

    // Clear the AsyncStorage if all moments were synced successfully
    try {
      await AsyncStorage.multiSet([
        ['offline_checkins', JSON.stringify([])],
        ['offline_checkouts', JSON.stringify([])],
        ['offline_moments', JSON.stringify([])]
      ]);
      console.log('Cleared stored moments from AsyncStorage');
    } catch (error) {
      console.error('Error clearing stored moments:', error);
    }
  };
}

// Offline check-in actions
export const offlineCheckIn = (people) => {
  return async (dispatch) => {
    try {
      dispatch({ type: OFFLINE_CHECKIN });

      // Update local cache
      const timestamp = moment().valueOf();
      const updates = people.map(person => ({
        ...person,
        checkedIn: true,
        checkInTime: timestamp,
      }));

      // Save to AsyncStorage for person data updates
      const key = 'offline_checkins';
      const existingData = await AsyncStorage.getItem(key);
      const existingCheckins = existingData ? JSON.parse(existingData) : [];

      await AsyncStorage.setItem(key, JSON.stringify([
        ...existingCheckins,
        ...updates
      ]));

      // Also save to AsyncStorage for check-in moments
      // This is separate from the person data and is used for syncing check-in moments
      const checkInsKey = 'offline_check_ins';
      const existingCheckInsData = await AsyncStorage.getItem(checkInsKey);
      const existingCheckInMoments = existingCheckInsData ? JSON.parse(existingCheckInsData) : [];

      // Create check-in moment records for each person
      const checkInMoments = updates.map(person => ({
        _id: person._id,
        personId: person._id,
        firstName: person.firstName,
        lastName: person.lastName,
        checkInTime: timestamp,
        checkInGroupId: person.checkInGroupId,
        checkInGroupName: person.checkInGroupName,
        createdAt: timestamp
      }));

      await AsyncStorage.setItem(checkInsKey, JSON.stringify([
        ...existingCheckInMoments,
        ...checkInMoments
      ]));

      console.log(`Saved ${checkInMoments.length} check-in moments to AsyncStorage`);

      // Update the local Meteor collection for each person
      updates.forEach(person => {
        const p = Meteor.collection('people').findOne(person._id);
        if (p) {
          p.checkedIn = true;
          p.checkInTime = timestamp;
          p.checkInGroupId = person.checkInGroupId;
          p.checkInGroupName = person.checkInGroupName;
          p.checkedInOutTime = timestamp;
          Meteor.collection('people').saveLocal(p);
          console.log(`Updated local Meteor collection for person: ${person._id}`);
        } else {
          console.warn(`Person not found in local Meteor collection: ${person._id}`);
          // If the person doesn't exist in the collection, insert them
          Meteor.collection('people').insert({
            ...person,
            checkedIn: true,
            checkInTime: timestamp,
            checkedInOutTime: timestamp,
            type: 'person'
          });
          console.log(`Inserted person into local Meteor collection: ${person._id}`);
        }
      });

      dispatch({
        type: OFFLINE_CHECKIN_SUCCESS,
        payload: updates
      });

      // Update the Redux state with the fresh list of people
      dispatch(updateOfflinePeople());

    } catch (error) {
      dispatch({
        type: OFFLINE_CHECKIN_ERROR,
        error: error.message
      });
      throw error;
    }
  };
};

// Offline check-out actions
export const offlineCheckOut = (person, checkOutData) => {
  return async (dispatch) => {
    try {
      // Update local cache - create an updated person object with checked out status
      const update = {
        ...person,
        checkedIn: false,
        checkInGroupId: null,
        checkInGroupName: null,
        checkedInOutTime: moment().valueOf()
      };

      // Update the local Meteor collection
      const p = Meteor.collection('people').findOne(person._id || person.tagId);
      if (p) {
        p.checkedIn = false;
        p.checkInGroupId = null;
        p.checkInGroupName = null;
        p.checkedInOutTime = moment().valueOf();
        Meteor.collection('people').saveLocal(p);
      }

      // Save to AsyncStorage for syncing later
      const key = 'offline_checkouts';
      const existingData = await AsyncStorage.getItem(key);
      const existingCheckouts = existingData ? JSON.parse(existingData) : [];

      // Add the checkout data with timestamp
      const checkoutRecord = {
        ...update,
        checkOutTime: moment().valueOf(),
        checkOutData: checkOutData
      };

      await AsyncStorage.setItem(key, JSON.stringify([
        ...existingCheckouts,
        checkoutRecord
      ]));

      console.log('Updated cached person data for checkout and saved to AsyncStorage:', checkoutRecord);

      // Update the Redux state with the fresh list of people
      dispatch(updateOfflinePeople());

      return true;
    } catch (error) {
      console.error('Error in offlineCheckOut:', error);
      return false;
    }
  };
};

// Action to refresh the offlinePeople state from the Meteor collection
export const updateOfflinePeople = () => {
  return (dispatch) => {
    dispatch({ type: OFFLINE_UPDATE_PEOPLE });
  };
};
