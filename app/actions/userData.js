import _ from 'lodash';
import { Al<PERSON>, Platform } from 'react-native';
import Meteor from 'react-native-meteor';
import * as environmentSettings from '../shared/Settings';
import moment from 'moment-timezone';
import { AppState } from 'react-native';
import {
  AUTH_GET_USERDATA,
  AUTH_RECEIVE_USERDATA,
  AUTH_RECEIVE_USER_TOKEN,
  AUTH_CLEAR_DATA,
  AUTH_REHYDRATE_DATA,
  AUTH_C<PERSON>AR_BADGE,
  AUTH_TOGGLE_OFFLINEMODE,
  AUTH_TOGGLE_ONLINEMODE,
  AUTH_RECEIVE_STAFFADMINPEOPLE,
  AUTH_SET_PEOPLE_SCOPE,
  AUTH_USER_DATA_CALL,
  AUTH_C<PERSON>AR_BACKGROUND,
  AUTH_TRACK_BACKGROUND,
  AUTH_PROCESS_BACKGROUND,
  AUTH_SET_PIN_PROMPT,
  AUTH_SET_LOADING,
  AUTH_SET_SWITCHABLE_ORGS,
  AUTH_SET_SWITCHABLE_MEMBERSHIPS,
  AUTH_SET_ACTIVE_ACCOUNTS, SET_UNREAD_MESSAGES,
} from '../utility/actions';

import { setOptions, fetchAndParse } from '../utility/httpFetch';
import Bugsnag from "@bugsnag/react-native";

export const userLoginWithPin = (pin, supplemental) => {
  return (dispatch, getState) => {
    const options = setOptions('POST', null, { pinCode: pin, supplementalPin: supplemental, orgId: environmentSettings.WL_PIN_LOGIN_ORG_ID });
    return fetch(`${environmentSettings.APP_URL}/mobile/pinCodeLogin`, options)
      .then(async (response) => {
        const json = await response.json();
        if (!response.ok) {
          Alert.alert(json?.error)
          return null;
        }
        // console.log(json?.token ?? "no token")
        return json?.token ?? null;
      })
      .catch((e) => {
        console.log("unexpected error in userLoginWithPin", e)
      });
  }
}

export const getUserData = (rt) => {
  return (dispatch, getState) => {
    const {resumeToken, lastUserDataCall} = getState().auth;
    const tokenToUse = rt || resumeToken;
    const options = setOptions('POST', null, { resumeToken: tokenToUse });
    if (!tokenToUse) {
      return;
    }
    dispatch({ type: AUTH_RECEIVE_USER_TOKEN, payload: { resumeToken: tokenToUse } });
    dispatch({ type: AUTH_USER_DATA_CALL, payload: { lastUserDataCall: new moment() } });
    fetchAndParse(`${environmentSettings.APP_URL}/mobile/userData`, options, dispatch)
      .then((response) => {
        let isError = response.status === 403 || response.status === 404 || response.status === 500;
        if(isError) {
          Alert.alert(
            "We're sorry, but something has gone wrong.",
            'Please check your network connection and log in again.',
            [
              {
                text: 'OK',
                onPress: () => {
                  dispatch({type: AUTH_CLEAR_DATA, payload: {loading: false}});
                  dispatch({type: AUTH_USER_DATA_CALL, payload: {lastUserDataCall: null}});
                  Meteor.accountSwitchLogout();
                },
              },
            ],
          );
        } else {
          const userInfo = _.get(response, 'user', null);
          const personInfo = _.get(response, 'person', null);
          const orgInfo = _.get(response, 'org', null);
          const momentDefInfo = _.get(response, 'momentDefinitions', null);
          const appHelpInfo = _.get(response, 'appHelpInfo.data', []);
          const appHelpBadgeDate = _.get(
            response,
            'appHelpInfo.mobileBadgeDate',
            null,
          );
          const allPeople = response?.allPeople ?? [];
          const allGroups = response?.allGroups ?? [];
          const allFood = response?.allFood ?? [];
          const allCurriculums = response?.allCurriculums ?? [];
          const allRelationships = response?.allRelationships ?? [];
          const directory = response?.directory ?? [];
          const dashboard = response?.dashboard ?? {};
          dispatch({
            type: AUTH_RECEIVE_USERDATA,
            payload: {
              userInfo,
              personInfo,
              orgInfo,
              momentDefInfo,
              appHelpInfo,
              appHelpBadgeDate,
              allPeople,
              allGroups,
              allFood,
              allCurriculums,
              directory,
              allRelationships,
              dashboard,
            },
          }).then(() => {
            try {
              fetchSwitchableOrgs();
              fetchMemberships();
            } catch (error) {
              console.log('error fetching switchable orgs/ Memberships', error);
              return;
            }
            dispatch(setLoading(false));
          });
        }
      })
      .catch((e) => {
        if (e && e.statusCode == 404) {
          dispatch({ type: AUTH_CLEAR_DATA, payload: { loading: false }});
          dispatch({ type: AUTH_USER_DATA_CALL, payload: { lastUserDataCall: null }});
        }
      });
  };
};

export const fetchPeopleChanges = (rt) => {
  return (dispatch, getState) => {
    const { resumeToken, lastUserDataCall, peopleScope } = getState().auth;
    const now = new moment();
    const tokenToUse = rt || resumeToken;
    if (!tokenToUse) {
      return;
    }
    const options = setOptions('POST', null, { resumeToken: tokenToUse, peopleScope });
    const delay = peopleScope == "all" ? 1000 : 150;

    if (lastUserDataCall && now.diff(new moment(lastUserDataCall)) < delay) {
      return;
    }
    dispatch({ type: AUTH_USER_DATA_CALL, payload: { lastUserDataCall: new Date().valueOf() } });
    fetchAndParse(`${environmentSettings.APP_URL}/mobile/staffAdminPeople`, options, dispatch)
      .then((response) => {

        const allPeople = response?.allPeople ?? [];
        const dashboard = response?.dashboard ?? {};
        const allGroups = response?.allGroups ?? [];

        dispatch({ type: AUTH_RECEIVE_STAFFADMINPEOPLE, payload: {allPeople, dashboard, allGroups} });
      })
      .catch((e) => {
        if (e && e.statusCode == 404) {
          // Alert.alert("User not found - please login again");
          dispatch({ type: AUTH_CLEAR_DATA, payload: { loading: false }});
        }
      });
  };
}

export const setPeopleScope = (scope) => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_SET_PEOPLE_SCOPE, payload: { scope } });
  }
};

export const clearUserData = (loading = false) => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_CLEAR_DATA, payload: { loading } });
  }
};

export const rehydrateUserData = () => {
  console.log("rehydrateUserData")


  return (dispatch, getState) => {
    dispatch({ type: AUTH_REHYDRATE_DATA });
  }
};

export const clearBadge = () => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_CLEAR_BADGE });
  }
}

export const toggleOfflineMode = () => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_TOGGLE_OFFLINEMODE})
  }
}

export const toggleOnlineMode = () => {
  return async (dispatch, getState) => {
    dispatch({ type: AUTH_TOGGLE_ONLINEMODE});

    // Check if there are moments to sync and sync them
    const { momentsToSync } = getState().offline;
    const org = Orgs.current();

    // Sync moments if there are any
    if (momentsToSync && momentsToSync.length > 0) {
      const { syncMoments } = require('./offline');
      dispatch(syncMoments(org));
    }

    // Also sync offline check-ins
    try {
      const { syncOfflineCheckIns, syncOfflineCheckOuts } = require('../screens/Offline/syncHelpers');

      // Sync check-ins
      const checkInResult = await syncOfflineCheckIns();
      console.log('Check-in sync result:', checkInResult);

      // Sync check-outs
      const checkOutResult = await syncOfflineCheckOuts();
      console.log('Check-out sync result:', checkOutResult);
    } catch (error) {
      console.error('Error syncing check-ins/outs during toggleOnlineMode:', error);
    }
  };
}

export const trackAppBackground = () => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_TRACK_BACKGROUND });
  }
}

export const clearAppBackground = () => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_CLEAR_BACKGROUND });
  };
}

export const processBackgroundAuth = () => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_PROCESS_BACKGROUND });
  };
}

export const setPinPromptAuth = () => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_SET_PIN_PROMPT });
  };
}

export const setLoading = (loading) => {
  return (dispatch, getState) => {
    dispatch({ type: AUTH_SET_LOADING, payload: { loading } });
  };
}

export const fetchSwitchableOrgs = () => {
  return async (dispatch) => {
    try {
      Meteor.call('getSwitchableOrgs', true, (err, result) => {
        if (err) {
          console.log('error fetching switchable orgs', err);
          result = [{name: 'No sites found', _id: null}];
        } else {
          let sortedByAlpha = result.sort(function (a, b) {
            a = a.name.toLowerCase();
            b = b.name.toLowerCase();
            if (a == b) return 0;
            return a < b ? -1 : 1;
          });
          if (Platform.OS === 'ios') {
            sortedByAlpha.unshift({name: 'Select Site', _id: null});
          }
          dispatch({
            type: AUTH_SET_SWITCHABLE_ORGS,
            payload: {switchableOrgs: sortedByAlpha},
          });
        }
      })

    } catch (error) {
      console.log('error fetching switchable orgs', error);
    }
  };
};

export const fetchMemberships = () => {
  return async (dispatch) => {
    try {
      Meteor.call('getSwitchableMemberships', async (err, result) => {
        dispatch({
          type: AUTH_SET_SWITCHABLE_MEMBERSHIPS,
          payload: {
            switchableMemberships: result,
          },
        })
      });
    } catch (error) {
      console.log('error fetching memberships', error);
    }
  };
};

export const fetchActiveAccounts = () => {
  return async (dispatch) => {
    try {
      let activeAccounts = await Meteor.getAllLocalStorageAccounts();
      dispatch({type: AUTH_SET_ACTIVE_ACCOUNTS, payload: {activeAccounts}});
    } catch (error) {
      console.log('error fetching active accounts', error);
    }
  }
}

export const removeAccounts = (currentUserId) => {
  console.log("Removing all accounts except ", currentUserId)
  return async (dispatch) => {
    try {
      await Meteor.removeAllOtherAccounts(currentUserId);
    } catch (error) {
      console.log('error removing accounts', error);
    }
    dispatch(fetchActiveAccounts());
  }
}

export const checkUnreadMessages = () => {
  return (dispatch, getState) => {
    const state = getState();
    const appState = AppState.currentState;

    // Only check if app is active
    if (appState !== 'active') {
      return;
    }
    console.log("checkUnreadMessages called")
    Meteor.call('checkForUnreadMessages', (err, res) => {
      if (err) {
        console.error('Error checking for unread messages:', err?.message);
      } else if (state.auth.hasUnread !== res) {
        console.log(`Unread messages status changed: ${res ? 'New messages' : 'No unread messages'}`);
      }
      dispatch({type: SET_UNREAD_MESSAGES, payload: {hasUnread: res}});
    });
  };
};
