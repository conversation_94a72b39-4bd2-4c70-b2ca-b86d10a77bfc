import AsyncStorage from '@react-native-async-storage/async-storage';
import Meteor from 'react-native-meteor';
import { updateCachedChildren } from './cacheHelpers';

/**
 * Groups check-ins by their timestamp and other shared properties
 * @param {Array} checkIns - Array of check-in records
 * @returns {Array} Array of grouped check-ins
 */
const groupCheckInsByBatch = (checkIns) => {
  const batchMap = new Map();

  checkIns.forEach(checkIn => {
    // Create a key based on shared properties
    const batchKey = `${checkIn.timestamp}_${checkIn.groupId}_${checkIn.comments}`;

    if (!batchMap.has(batchKey)) {
      batchMap.set(batchKey, {
        timestamp: checkIn.timestamp,
        groupId: checkIn.groupId,
        groupName: checkIn.groupName,
        comments: checkIn.comments,
        prettyTime: checkIn.prettyTime,
        people: []
      });
    }

    batchMap.get(batchKey).people.push({
      personId: checkIn.personId,
      firstName: checkIn.firstName,
      lastName: checkIn.lastName
    });
  });

  return Array.from(batchMap.values());
};

/**
 * Gets the Meteor collection for people with proper error handling
 * @returns {Object|null} Meteor collection or null if not available
 */
const getPeopleCollection = () => {
  try {
    return Meteor.collection('people');
  } catch (error) {
    console.error('Error getting people collection:', error);
    return null;
  }
};

/**
 * Safely queries the Meteor collection
 * @param {string} personId - The person ID to find
 * @returns {Object|null} The person document or null if not found
 */
const findPersonInCollection = (personId) => {
  try {
    const collection = getPeopleCollection();
    if (!collection) {
      console.warn('People collection not available');
      return null;
    }

    // In offline mode, we need to use the raw documents
    const docs = collection._collection.docs;
    return docs.get(personId);
  } catch (error) {
    console.error('Error finding person in collection:', error);
    return null;
  }
};

/**
 * Updates a person in the Meteor collection
 * @param {string} personId - The person ID to update
 * @param {Object} updates - The updates to apply
 * @returns {boolean} Whether the update was successful
 */
const updatePersonInCollection = (personId, updates) => {
  try {
    const collection = getPeopleCollection();
    if (!collection) {
      console.warn('People collection not available for update');
      return false;
    }

    // In offline mode, we need to update the raw documents
    const docs = collection._collection.docs;
    const existing = docs.get(personId);
    if (existing) {
      const updated = { ...existing, ...updates };
      docs.set(personId, updated);
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error updating person in collection:', error);
    return false;
  }
};

/**
 * Attempts to check in a person with retry logic
 * @param {Object} checkInData - The check-in data
 * @param {number} maxRetries - Maximum number of retry attempts
 * @returns {Promise<Object>} The result of the check-in attempt
 */
const attemptCheckIn = async (checkInData, maxRetries = 3) => {
  let lastError = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      console.log(`Check-in attempt ${attempt}/${maxRetries} for person:`, checkInData.personId);

      const result = await new Promise((resolve, reject) => {
        Meteor.call("checkIn", checkInData, (error, result) => {
          if (error) {
            console.log(`Attempt ${attempt} error:`, error);

            // If already checked in, treat as success
            if (error.reason === "Person already checked in") {
              console.log("Person already checked in on server:", checkInData.personId);
              resolve({ success: true, alreadyCheckedIn: true });
              return;
            }

            // For server errors, we'll retry
            if (error.error === 500 || error.message?.includes('Internal server error')) {
              reject(error);
              return;
            }

            // For other errors, we'll stop retrying
            resolve({ success: false, error });
          } else {
            console.log(`Attempt ${attempt} successful for:`, checkInData.personId);
            resolve({ success: true, result });
          }
        });
      });

      if (result.success) {
        return result;
      }

      lastError = result.error;

    } catch (error) {
      console.log(`Attempt ${attempt} failed, ${attempt < maxRetries ? 'retrying...' : 'giving up.'}`);
      lastError = error;

      // Wait before retrying (exponential backoff)
      if (attempt < maxRetries) {
        const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
    }
  }

  throw lastError;
};

/**
 * Processes a batch of check-ins for multiple children
 * @param {Object} batchCheckIn - The batch check-in data
 * @returns {Promise<{success: boolean, results: Array}>} Processing results
 */
const processBatchCheckIn = async (batchCheckIn) => {
  const results = [];
  console.log("Processing batch check-in:", batchCheckIn);

  try {
    // Process each person in the batch
    for (const person of batchCheckIn.people) {
      const checkInData = {
        personId: person.personId,
        groupId: batchCheckIn.groupId,
        groupName: batchCheckIn.groupName,
        prettyTime: batchCheckIn.prettyTime,
        comments: batchCheckIn.comments,
        timestamp: batchCheckIn.timestamp
      };

      try {
        // First check if person is already checked in
        console.log("Checking person status:", person.personId);
        const personDoc = findPersonInCollection(person.personId);
        console.log("Current person doc:", personDoc);

        if (personDoc?.checkedIn) {
          console.log("Person already checked in locally:", person.personId);
          results.push({
            personId: person.personId,
            success: true,
            error: null,
            wasAlreadyCheckedIn: true
          });
          continue;
        }

        console.log("Attempting check-in for:", checkInData);
        const checkInResult = await attemptCheckIn(checkInData);

        if (checkInResult.success) {
          if (!checkInResult.alreadyCheckedIn) {
            // Update local collection
            const updated = updatePersonInCollection(person.personId, {
              checkedIn: true,
              checkInGroupId: checkInData.groupId,
              checkInGroupName: checkInData.groupName
            });
            console.log("Local collection update result:", updated);
          }

          results.push({
            personId: person.personId,
            success: true,
            error: null,
            wasAlreadyCheckedIn: checkInResult.alreadyCheckedIn
          });
        } else {
          throw checkInResult.error;
        }
      } catch (error) {
        console.error("Individual check-in error:", error);
        results.push({
          personId: person.personId,
          success: false,
          error: error.reason || error.message || 'Unknown error'
        });
      }
    }

    return {
      success: results.every(r => r.success),
      results
    };
  } catch (error) {
    console.error('Batch processing error:', error);
    return {
      success: false,
      results: batchCheckIn.people.map(p => ({
        personId: p.personId,
        success: false,
        error: error.reason || error.message || 'Batch processing failed'
      }))
    };
  }
};

/**
 * Syncs all stored offline checkouts with the server
 * @returns {Promise<{success: boolean, message: string}>} Sync result
 */
export const syncOfflineCheckOuts = async () => {
    console.log("=== Starting syncOfflineCheckOuts ===");
    try {
        // Get stored offline checkouts
        console.log("Fetching offline checkouts from storage...");
        const offlineCheckOutsJson = await AsyncStorage.getItem('offline_checkouts');
        console.log("Raw storage data:", offlineCheckOutsJson);

        if (!offlineCheckOutsJson) {
            console.log("No offline checkouts found in storage");
            return { success: true, message: 'No offline checkouts to sync' };
        }

        const offlineCheckOuts = JSON.parse(offlineCheckOutsJson);
        if (!offlineCheckOuts || !offlineCheckOuts.length) {
            console.log("No valid offline checkouts found");
            return { success: true, message: 'No valid offline checkouts to sync' };
        }

        console.log(`Starting sync of ${offlineCheckOuts.length} offline checkouts`);

        // Process each checkout
        const results = await Promise.all(
            offlineCheckOuts.map(async (checkout) => {
                try {
                    // Create a moment object from the stored checkout data
                    const checkOutData = {
                        personId: checkout._id,
                        offlineType: 'checkOut',
                        momentType: 'checkOut',
                        checkOutTime: checkout.checkOutTime,
                        createdAt: checkout.checkOutTime,
                        ...checkout.checkOutData
                    };

                    // Call the server method
                    const result = await new Promise((resolve) => {
                        Meteor.call('checkOut', checkOutData, (error, result) => {
                            if (error) {
                                console.log('Error syncing checkout:', error);
                                resolve({ success: false, error, checkout });
                            } else {
                                console.log('Checkout synced successfully');
                                resolve({ success: true, checkout });
                            }
                        });
                    });

                    return result;
                } catch (error) {
                    console.error('Error processing checkout:', error);
                    return { success: false, error, checkout };
                }
            })
        );

        // Count successes and failures
        const successful = results.filter(r => r.success);
        const failed = results.filter(r => !r.success);

        // Clear successful checkouts from storage
        if (successful.length > 0) {
            const remainingCheckouts = offlineCheckOuts.filter(checkout =>
                !successful.some(s => s.checkout._id === checkout._id &&
                                    s.checkout.checkOutTime === checkout.checkOutTime)
            );

            await AsyncStorage.setItem('offline_checkouts', JSON.stringify(remainingCheckouts));
            console.log(`Removed ${successful.length} successful checkouts from storage`);
        }

        // Generate result message
        const message = `Synced ${successful.length} checkouts, ${failed.length} failed`;
        console.log("Final sync message:", message);

        return {
            success: failed.length === 0,
            message,
            syncedCount: successful.length,
            failedCount: failed.length,
            failedDetails: failed
        };
    } catch (error) {
        console.error('Sync error:', error);
        return {
            success: false,
            message: 'Failed to sync checkouts: ' + (error.reason || error.message),
            syncedCount: 0,
            failedCount: 0,
            failedDetails: []
        };
    }
};

/**
 * Syncs all stored offline check-ins with the server
 * @returns {Promise<{success: boolean, message: string}>} Sync result
 */
export const syncOfflineCheckIns = async () => {
    console.log("=== Starting syncOfflineCheckIns ===");
    try {
        // Get stored offline check-ins
        console.log("Fetching offline check-ins from storage...");
        const offlineCheckInsJson = await AsyncStorage.getItem('offline_check_ins');
        console.log("Raw storage data:", offlineCheckInsJson);

        if (!offlineCheckInsJson) {
            console.log("No offline check-ins found in storage");
            return { success: true, message: 'No offline check-ins to sync' };
        }

        const offlineCheckIns = JSON.parse(offlineCheckInsJson);
        console.log("Parsed check-ins:", offlineCheckIns);

        if (!offlineCheckIns.length) {
            console.log("Empty check-ins array");
            return { success: true, message: 'No offline check-ins to sync' };
        }

        console.log(`Starting sync of ${offlineCheckIns.length} offline check-ins`);

        // Group check-ins by batch
        const batches = groupCheckInsByBatch(offlineCheckIns);
        console.log(`Grouped into ${batches.length} batches:`, batches);

        // Process each batch
        console.log("Processing batches...");
        const batchResults = await Promise.all(
            batches.map(async (batch) => {
                console.log("Processing batch:", batch);
                const result = await processBatchCheckIn(batch);
                console.log("Batch result:", result);
                return {
                    batch,
                    ...result
                };
            })
        );
        console.log("All batch results:", batchResults);

        // Collect all results
        const allResults = batchResults.flatMap(br => br.results);
        console.log("All individual results:", allResults);

        const successfulCheckIns = allResults.filter(r => r.success);
        const failedCheckIns = allResults.filter(r => !r.success);

        console.log("Successful check-ins:", successfulCheckIns);
        console.log("Failed check-ins:", failedCheckIns);

        // Update storage to only keep failed check-ins
        if (failedCheckIns.length > 0) {
            const failedOriginals = offlineCheckIns.filter(
                checkIn => failedCheckIns.some(f => f.personId === checkIn.personId)
            );
            console.log("Saving failed check-ins back to storage:", failedOriginals);
            await AsyncStorage.setItem('offline_check_ins', JSON.stringify(failedOriginals));
        } else {
            console.log("No failed check-ins, clearing storage");
            await AsyncStorage.removeItem('offline_check_ins');
        }

        // Generate message using the new format function
        const message = formatSyncMessage(successfulCheckIns, failedCheckIns);
        console.log("Final sync message:", message);

        const result = {
            success: failedCheckIns.length === 0,
            message,
            syncedCount: successfulCheckIns.filter(r => !r.wasAlreadyCheckedIn).length,
            alreadyCheckedInCount: successfulCheckIns.filter(r => r.wasAlreadyCheckedIn).length,
            failedCount: failedCheckIns.length,
            failedDetails: failedCheckIns
        };
        console.log("Final sync result:", result);
        return result;
    } catch (error) {
        console.error('Sync error:', error);
        return {
            success: false,
            message: 'Failed to sync check-ins: ' + (error.reason || error.message),
            syncedCount: 0,
            alreadyCheckedInCount: 0,
            failedCount: 0,
            failedDetails: []
        };
    }
};

/**
 * Gets the count of pending offline check-ins
 * @returns {Promise<number>} Number of pending check-ins
 */
export const getPendingCheckInsCount = async () => {
  try {
    const offlineCheckInsJson = await AsyncStorage.getItem('offline_check_ins');
    if (!offlineCheckInsJson) return 0;
    const offlineCheckIns = JSON.parse(offlineCheckInsJson);
    return offlineCheckIns.length;
  } catch (error) {
    console.error('Error getting pending check-ins count:', error);
    return 0;
  }
};

/**
 * Formats the sync results message
 * @param {Array} successfulCheckIns - Array of successful check-ins
 * @param {Array} failedCheckIns - Array of failed check-ins
 * @returns {string} Formatted message
 */
const formatSyncMessage = (successfulCheckIns, failedCheckIns) => {
  const alreadyCheckedIn = successfulCheckIns.filter(r => r.wasAlreadyCheckedIn).length;
  const newlyCheckedIn = successfulCheckIns.length - alreadyCheckedIn;

  let message = [];
  if (newlyCheckedIn > 0) {
    message.push(`Successfully synced ${newlyCheckedIn} check-ins`);
  }
  if (alreadyCheckedIn > 0) {
    message.push(`${alreadyCheckedIn} already checked in`);
  }
  if (failedCheckIns.length > 0) {
    const errorSummary = failedCheckIns
      .map(f => `${f.personId}: ${f.error}`)
      .join('; ');
    message.push(`Failed ${failedCheckIns.length}: ${errorSummary}`);
  }

  return message.join('. ');
};