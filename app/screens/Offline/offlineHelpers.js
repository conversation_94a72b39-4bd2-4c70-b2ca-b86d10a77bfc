import {Alert} from 'react-native';
import {toggleOfflineMode, toggleOnlineMode} from '../../actions/userData';
import {getCachedPeople, updateCachedChildren} from './cacheHelpers';
import {
  offlineCheckIn,
  offlineCheckOut,
  nameToFace,
  saveMoment,
  addSleepMoments
} from "../../actions/offline";

export const accountLongPress = props => {
  const {offlineMode} = props;
  Alert.alert(
    `${offlineMode ? 'Go Online' : 'Go Offline'}`,
    `${offlineMode ? 'Be sure to sync any pending moments!' : 'limited functionality is available in offline mode. Would you like to proceed?'}`,
    [
      {text: 'Cancel', style: 'cancel'},
      {
        text: 'OK',
        onPress: () => {
          props.dispatch(
            offlineMode ? toggleOnlineMode() : toggleOfflineMode(),
          );
        },
      },
    ],
    {cancelable: false},
  );
};

/**
 * Common navigation helper to navigate to the tagger component
 * @param {Object} params - Navigation parameters
 * @param {Object} params.navigation - Navigation object
 * @param {Function} params.onSave - Callback function when people are selected
 * @param {Array} params.people - List of people to display in the tagger
 * @param {Object} params.currentOrg - Current organization
 * @param {String} params.title - Title for the tagger screen
 * @param {Boolean} params.checkInNotRequired - Whether check-in is required for the moment
 */
export const navigateToTagger = ({
  navigation,
  onSave,
  people,
  currentOrg,
  title = 'Tag People',
  checkInNotRequired = false,
}) => {
  navigation.navigate('offlineTagger', {
    onSave,
    taggedPeople: [],
    peopleOnly: true,
    currentOrg,
    people,
    title,
    checkInNotRequired,
  });
};

/**
 * Common navigation helper to navigate to the moment entry component
 * @param {Object} params - Navigation parameters
 * @param {Object} params.navigation - Navigation object
 * @param {Object} params.currentOrg - Current organization
 * @param {String} params.momentType - Type of moment
 * @param {Array} params.taggedPeople - List of tagged people
 * @param {String} params.taggedPeopleLabel - Label for the tagged people
 * @param {Object} params.currentPerson - Information about the person
 * @param {Function} params.dispatch - Dispatch function
 */
const navigateToMomentEntry = ({navigation, currentOrg, momentType, taggedPeople, taggedPeopleLabel, currentPerson, dispatch}) => {
  navigation.navigate('offlineMomentEntry', {
    taggedPeople,
    taggedPeopleLabel,
    userPerson: currentPerson,
    newMomentType: momentType.toLowerCase(),
    hidePeopleChooser: true,
    onSave: (momentData) => {
      dispatch(saveMoment(momentType, {
        momentType,
        taggedPeople,
        currentOrg,
        personId: currentPerson._id,
        date: momentData.date,
        time: momentData.time,
        momentFieldValues: momentData.momentFieldValues,
        createdAt: momentData.createdAt || new Date().valueOf(),
        attachedMedia: momentData.attachedMedia || {},
      }));
      navigation.navigate('offlineHome');
    }
  });
};

/**
 * Handler for Check In moment
 */
export const onCheckIn = ({navigation, dispatch, currentOrg}) => {
  // Get only checked-out children
  let checkedOutChildren = getCachedPeople(true)
  if (checkedOutChildren.length === 0) {
    Alert.alert(
      'No Children Available',
      'All children are already checked in.',
    );
    return;
  }

  // Navigate to the children tagger screen with check-in mode
  navigation.navigate('childrenTagger', {
    title: 'Select Children to Check In',
    type: 'checkIn', // Not strictly necessary as this is the default mode
    taggedPeople: [], // Initially no children are selected
    availablePeople: checkedOutChildren,
    currentOrg,
    onSave: checkinData => {
      console.log('Saving check-in data:', checkinData);

      if (!checkinData.selectedChildren?.length) {
        navigation.navigate('offlineHome');
        return;
      }

      // Create updates for each selected child
      const timestamp = checkinData.timestamp;
      const updates = checkinData.selectedChildren.map(child => ({
        ...child,
        checkedIn: true,
        checkInTime: timestamp,
        checkInGroupId: checkinData.groupId || child.defaultGroupId || null,
        checkInGroupName:
          checkinData.groupName || child.defaultGroupName || null,
      }));

      // Dispatch the action to store in AsyncStorage and update the local Meteor collection
      dispatch(offlineCheckIn(updates));

      // Save the check-in moment for syncing
      dispatch(saveMoment('checkIn', {
        ...checkinData,
        currentOrg,
        date: new Date(),
        time: new Date(),
        createdAt: timestamp || new Date().valueOf(),
        momentType: 'checkIn',
        offlineType: 'checkIn',
        taggedPeople: checkinData.selectedChildren,
      }));

      // Note: We don't need to call updateCachedChildren here anymore
      // because offlineCheckIn now updates the local Meteor collection
      console.log(`Updated cached data for ${updates.length} people via offlineCheckIn`);

      // Navigate back to home
      navigation.navigate('offlineHome');
    },
  });
};

/**
 * Handler for Check Out moment
 */
export const onCheckOut = ({navigation, dispatch, currentOrg, cachedPeople}) => {
  // Get only checked-in children
  const checkedInPeople = getCachedPeople()
    .filter(p => p.checkedIn)

  if (checkedInPeople.length === 0) {
    Alert.alert(
      'No People Available',
      'There are no checked-in people to check out.',
    );
    return;
  }

  // Navigate to the children tagger screen with checkout mode
  navigation.navigate('childrenTagger', {
    title: 'Select People to Check Out',
    type: 'checkOut',
    taggedPeople: [],
    currentOrg,
    people: checkedInPeople,
    onSave: checkoutData => {
      console.log('Saving checkout data:', checkoutData);

      // Get the person object from the ID
      const personId = checkoutData.personId;
      const person = cachedPeople.find(p => p._id === personId);

      if (!person) {
        console.error('Person not found in cached people:', personId);
        return;
      }

      // First save the checkout moment for syncing
      dispatch(saveMoment('checkOut', {
        ...checkoutData,
        currentOrg,
        personId: person._id,
        date: checkoutData.date || new Date(),
        time: checkoutData.time || new Date(),
        createdAt: new Date().valueOf(),
      }));

      // Then update the cached person data, save checkout data to AsyncStorage,
      // and update the local Meteor collection
      dispatch(offlineCheckOut(person, checkoutData));

      // Note: We don't need to call updateCachedChildren here anymore
      // because offlineCheckOut now updates the local Meteor collection
      console.log(`Updated cached data for person ${person._id} via offlineCheckOut`);

      navigation.navigate('offlineHome');
    },
  });
};

/**
 * Handler for Name to Face moment
 */
export const onNameToFace = ({navigation, dispatch, currentOrg, currentPerson}) => {
  const cachedPeople = getCachedPeople()

  // Make sure the current person has a group assigned
  if (!currentPerson.checkInGroupId) {
    Alert.alert(
      "Group Required",
      "You must be checked into a group to perform a Name to Face check."
    );
    return;
  }

  const childrenInCurrentGroup = cachedPeople.filter(p => p.checkInGroupId === currentPerson.checkInGroupId);

  // Check if there are any children in the current group
  if (childrenInCurrentGroup.length === 0) {
    Alert.alert(
      "No Children Found",
      "There are no children checked into your current group."
    );
    return;
  }

  const saveNameToFace = (taggedPeople) => {
    if (taggedPeople?.length === 0) {
      Alert.alert("No Children Selected", "Please select at least one child for Name to Face.");
      return;
    }

    const childrenNames = taggedPeople
      .map(child => child.tagLabel)
      .join(', ');

    dispatch(nameToFace(taggedPeople));

    Alert.alert(
      "Name to Face Recorded",
      `Name to Face for ${taggedPeople.length} children recorded:\n\n${childrenNames}\n\nThis will be synced when you go online.`
    );

    navigation.navigate('offlineHome')
  };

  navigateToTagger({
    navigation,
    onSave: saveNameToFace,
    people: childrenInCurrentGroup,
    currentOrg,
    title: 'Select Children for Name to Face',
  });
};

/**
 * Handler for Sleep moment
 */
export const onSleepMoment = ({navigation, dispatch, currentOrg, currentPerson}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    title: 'Select Children for Sleep Moment',
    onSave: (taggedPeople) => {
      console.log('Sleep moment tagged people:', taggedPeople);
      if (taggedPeople?.length === 0) return;
      const taggedPeopleLabel = taggedPeople.map(tag => tag.tagLabel).join(', ');
      navigateToMomentEntry({
        navigation,
        currentOrg,
        momentType: 'sleep',
        taggedPeople,
        taggedPeopleLabel,
        currentPerson,
        dispatch,
      })
    },
  });
};

/**
 * Handler for Potty moment
 */
export const onPottyMoment = ({navigation, dispatch, currentOrg, currentPerson}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    title: 'Select Children for Potty Moment',
    onSave: (taggedPeople) => {
      if (taggedPeople?.length === 0) return;
      navigateToMomentEntry({
        navigation,
        currentOrg,
        taggedPeople,
        currentPerson,
        dispatch,
        momentType: 'potty',
        taggedPeopleLabel: taggedPeople.map(tag => tag.tagLabel).join(', '),
      })
    },
  });
};

/**
 * Handler for Food moment
 */
export const onFoodMoment = ({navigation, currentOrg, currentPerson, dispatch}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    onSave: (taggedPeople) => {
      navigateToMomentEntry({
        navigation,
        currentOrg,
        taggedPeople,
        dispatch,
        currentPerson,
        momentType: 'food',
        taggedPeopleLabel: taggedPeople.map(tag => tag.tagLabel).join(', '),
      })
    },
    title: 'Select Children for Food Moment',
    checkInNotRequired: true,
  });
};

/**
 * Handler for Comment moment
 */
export const onCommentMoment = ({navigation, currentOrg, currentPerson, dispatch}) => {
  const people = getCachedPeople();

  navigateToTagger({
    navigation,
    people,
    currentOrg,
    onSave: (taggedPeople) => {
      navigateToMomentEntry({
        navigation,
        currentOrg,
        taggedPeople,
        dispatch,
        currentPerson,
        momentType: 'comment',
        taggedPeopleLabel: taggedPeople.map(tag => tag.tagLabel).join(', '),
      })
    },
    title: 'Select Children for Comment',
    checkInNotRequired: true,
  });
};
