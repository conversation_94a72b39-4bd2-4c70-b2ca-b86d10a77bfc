import React, { useState, useEffect } from 'react';
import { InteractionManager } from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Header, Content, Body, Title, Text, Left, Right, Item, Input, List, Icon, ListItem } from 'native-base';
import { SafeAreaView } from 'react-native-safe-area-context';
import colors from '../../../config/colors.json';
import { useSelector } from 'react-redux';
import { CommonActions } from '@react-navigation/native';
import { FontAwesomeIcon } from '@fortawesome/react-native-fontawesome';
import { faXmarkCircle } from '@fortawesome/free-solid-svg-icons';
import Nucleo from '../../../components/icons/Nucleo';
const Tagger = ({ navigation, route, props }) => {
	const people = route?.params?.people ?? [];
	const [loaded, setLoaded] = useState(false);
	const [taggedPeople, setTaggedPeople] = useState(route?.params?.taggedPeople ?? []);
	const [searchText, setSearchText] = useState('');

	const groups = useSelector(state => state.auth.offlineGroups);

	const userPerson = useSelector(state => state.auth.personInfo);

	useEffect(() => {
		InteractionManager.runAfterInteractions(() => {
			setLoaded(true);
		});
	}, []);

	// Handler functions
	const addPerson = (person) => {
		console.log("Adding person:", person);
		setTaggedPeople(prev => [...prev, person]);
	};

	const removePerson = (tagId) => {
		setTaggedPeople(prev => prev.filter(taggedPerson => taggedPerson.tagId !== tagId));
	};

	if (!loaded) return null;

	const peopleOnly = route?.params?.peopleOnly ?? false;
	const searchTerm = searchText.toLowerCase();

	// Helper function to get tag information
	const getTagInfo = ({tagId, tagLabel, firstName, lastName }) => {
    if (tagId?.includes('group')) {
      const taggedGroup = groups.find(
        g => g._id === tagId.replace('group|', ''),
      );
      if (taggedGroup) {
        tagLabel = `Group: ${taggedGroup.name}`;
        tagId = `group|${taggedGroup._id}`;
      }
    } else if (tagId.includes('role')) {
      const roleType = tagId.replace('role|', '');
      if (roleType === 'all_staff') {
        tagLabel = 'All Staff';
        tagId = 'role|all_staff';
      }
    } else {
      const taggedPerson = people.find(p => p._id === tagId);
      if (taggedPerson) {
        tagLabel = `${taggedPerson.firstName} ${taggedPerson.lastName}`;
        tagId = taggedPerson._id;
								firstName = taggedPerson.firstName;
								lastName = taggedPerson.lastName;
      }
    }

    return {tagLabel, tagId, firstName, lastName };
  };

	// Filter and prepare available people
	const availablePeople = people
		.filter(p => {
			// Check if this person is already in the tagged list
			const isAlreadyTagged = taggedPeople.some(tagged => tagged.tagId === p._id);
			return !isAlreadyTagged &&
				(searchTerm === '' ||
				p?.firstName?.toLowerCase?.()?.includes?.(searchTerm) ||
				p?.lastName?.toLowerCase?.()?.includes?.(searchTerm));
		})
		.map(p => {
			// Create a new object instead of mutating the original
			return {
				...p,
				listItem: {
					tagLabel: `${p.firstName} ${p.lastName}`,
					tagId: p._id
				}
			};
		});

	// Filter and prepare available groups
	const availableGroups = groups
		.filter(g => {
			// Check if this group is already in the tagged list
			const isAlreadyTagged = taggedPeople.some(tagged => tagged.tagId === `group|${g._id}`);
			return !isAlreadyTagged &&
				(searchTerm === '' || g?.name?.toLowerCase?.()?.includes?.(searchTerm));
		})
		.map(g => {
			// Create a new object instead of mutating the original
			return {
				...g,
				listItem: {
					tagLabel: `Group: ${g.name}`,
					tagId: `group|${g._id}`
				}
			};
		});

	// Build available items list
	let availableItems = [];

	// Add 'All Staff' if not already tagged and not peopleOnly mode
	const isAllStaffTagged = taggedPeople.some(tagged => tagged.tagId === 'role|all_staff');
	if (!isAllStaffTagged && !peopleOnly) {
		availableItems.push({ tagLabel: 'All Staff', tagId: 'role|all_staff' });
	}

	// Add primary groups (user's check-in and default groups)
	const primaryGroups = availableGroups
		.filter(g => g._id === userPerson.checkInGroupId || g._id === userPerson.defaultGroupId)
		.map(g => ({ ...g, used: true }));

	if (!peopleOnly) {
		availableItems = [
			...availableItems,
			...primaryGroups.map(g => g.listItem)
		];
	}

	// Add non-family people - this is the only place we should add regular people
	const nonFamilyPeople = availablePeople
		.filter(p => p.type !== 'family')
		.map(p => ({ ...p, used: true }));

	availableItems = [
		...availableItems,
		...nonFamilyPeople.map(p => p.listItem)
	];

	// Add remaining people (should be none after the above filter)
	const remainingPeople = availablePeople

	// Add remaining groups if not in peopleOnly mode
	if (!peopleOnly) {
		const remainingGroups = availableGroups
			.filter(g => !g.used)
			.map(g => g.listItem);

		availableItems = [...availableItems, ...remainingGroups];
	}

	return (
    <Container>
      <Header style={{
								backgroundColor: colors.white,
								flexDirection: 'row',
								justifyContent: 'space-between',
								alignItems: 'center',
						}}>
          <Button transparent onPress={() => navigation.goBack()}>
            <Nucleo name="icon-arrow-left" color={colors.primaryA} />
            <Text
														uppercase={false}
														numberOfLines={1}
														style={{marginLeft: 2, fontSize: 16, color: colors.primaryA}}
												>
              Cancel
            </Text>
          </Button>
          <Title style={{color: colors.primaryA}}>Tag People</Title>
          <Button
            transparent
            onPress={() => route?.params?.onSave(taggedPeople)}>
            <Text
														uppercase={false}
														numberOfLines={1}
														style={{marginLeft: 6, fontSize: 16, color: colors.primaryA}}
												>
              Done
            </Text>
          </Button>
      </Header>
      <Item>
        <Icon name="ios-search" />
        <Input
          placeholder="Search"
          clearButtonMode="always"
          onChangeText={setSearchText}
        />
      </Item>

      <Content>
        <List>
          {taggedPeople.map(person => {
            const {tagLabel, tagId} = getTagInfo(person);
            return (
              tagId && (
                <ListItem icon key={`${tagId}-tagged`}>
                  <Left>
                    <Icon
                      active
                      name="check-circle"
                      type="FontAwesome5"
                      style={{fontSize: 18, color: colors.limeGreen}}
                    />
                  </Left>
                  <Body>
                    <Text>{tagLabel}</Text>
                  </Body>
                  <Right>
                    <Button transparent onPress={() => removePerson(tagId)}>
                      <FontAwesomeIcon
                        icon={faXmarkCircle}
                        size={24}
                        color={colors.charcoalLighter}
                      />
                    </Button>
                  </Right>
                </ListItem>
              )
            );
          })}
          {availableItems.map((item, index) => (
            <ListItem
              onPress={() => addPerson(item)}
              key={`available-${item.tagId}-${index}`}>
              <Text testID='tag-item'>{item.tagLabel}</Text>
            </ListItem>
          ))}
        </List>
      </Content>
    </Container>
  );
};
export default Tagger;
