import React from 'react';
import { render, screen, fireEvent, waitFor, act } from '../../../testUtils';
import Offline from '../Offline';
import { Alert } from 'react-native';
import Meteor from 'react-native-meteor';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { userEvent } from '@testing-library/react-native';
import * as offlineActions from '../../../actions/offline';
import * as userDataActions from '../../../actions/userData';
import * as syncHelpers from '../syncHelpers';
import * as offlineHelpers from '../offlineHelpers';

// Mock the navigation
const mockNavigation = {
  navigate: jest.fn(),
  setParams: jest.fn(),
};

// Mock the route
const mockRoute = {
  params: {},
};

// Mock Alert
jest.spyOn(Alert, 'alert').mockImplementation((title, message, buttons) => {
  // Simulate pressing the last button (usually confirm)
  if (buttons && buttons.length > 0) {
    const lastButton = buttons[buttons.length - 1];
    if (lastButton.onPress) {
      lastButton.onPress();
    }
  }
});

// Mock the offline actions
jest.mock('../../../actions/offline', () => ({
  syncMoments: jest.fn(() => ({ type: 'SYNC_MOMENTS' })),
  sleepCheck: jest.fn(() => ({ type: 'SLEEP_CHECK' })),
}));

// Mock the userData actions
jest.mock('../../../actions/userData', () => ({
  toggleOnlineMode: jest.fn(() => ({ type: 'TOGGLE_ONLINE_MODE' })),
}));

// Mock the sync helpers
jest.mock('../syncHelpers', () => ({
  syncOfflineCheckIns: jest.fn().mockResolvedValue({ success: true, count: 2 }),
  syncOfflineCheckOuts: jest.fn().mockResolvedValue({ success: true, count: 1 }),
  getPendingCheckInsCount: jest.fn().mockResolvedValue(3),
}));

// Mock the offline helpers
jest.mock('../offlineHelpers', () => ({
  onCheckIn: jest.fn(),
  onCheckOut: jest.fn(),
  onNameToFace: jest.fn(),
  onSleepMoment: jest.fn(),
  onPottyMoment: jest.fn(),
  onFoodMoment: jest.fn(),
  onCommentMoment: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  multiSet: jest.fn().mockResolvedValue(null),
  getItem: jest.fn().mockResolvedValue(null),
}));

describe('Offline Screen', () => {
  // Setup mock Redux state
  const mockState = {
    offline: {
      momentsToSync: [
        { _id: 'moment1', type: 'sleep' },
        { _id: 'moment2', type: 'food' },
      ],
      momentsSyncing: false,
    },
    auth: {
      offlinePeople: [
        { _id: 'person1', firstName: 'John', lastName: 'Doe' },
        { _id: 'person2', firstName: 'Jane', lastName: 'Smith' },
      ],
      userInfo: { _id: 'user1' },
      personInfo: { _id: 'person1', firstName: 'Test', lastName: 'User' },
      orgInfo: { _id: 'org1', name: 'Test Org' },
    },
  };

  // Mock useSelector to return our mock state
  jest.spyOn(require('react-redux'), 'useSelector').mockImplementation(
    selector => selector(mockState)
  );

  // Mock useDispatch to return a jest function
  const mockDispatch = jest.fn();
  jest.spyOn(require('react-redux'), 'useDispatch').mockReturnValue(mockDispatch);

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders all offline moment buttons', () => {
    render(<Offline navigation={mockNavigation} />);

    expect(screen.getByText('Check In')).toBeTruthy();
    expect(screen.getByText('Check Out')).toBeTruthy();
    expect(screen.getByText('Name To Face')).toBeTruthy();
    expect(screen.getByText('Sleep Moments')).toBeTruthy();
    expect(screen.getByText('Potty Moment')).toBeTruthy();
    expect(screen.getByText('Food Moment')).toBeTruthy();
    expect(screen.getByText('Comment Moment')).toBeTruthy();
  });

  it('calls onCheckIn when Check In button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Check In'));
    
    expect(offlineHelpers.onCheckIn).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
    });
  });

  it('calls onCheckOut when Check Out button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Check Out'));
    
    expect(offlineHelpers.onCheckOut).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
      cachedPeople: mockState.auth.offlinePeople,
    });
  });

  it('calls onNameToFace when Name To Face button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Name To Face'));
    
    expect(offlineHelpers.onNameToFace).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
      currentPerson: mockState.auth.personInfo,
    });
  });

  it('calls onSleepMoment when Sleep Moments button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Sleep Moments'));
    
    expect(offlineHelpers.onSleepMoment).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
      currentPerson: mockState.auth.personInfo,
    });
  });

  it('calls onPottyMoment when Potty Moment button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Potty Moment'));
    
    expect(offlineHelpers.onPottyMoment).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
      currentPerson: mockState.auth.personInfo,
    });
  });

  it('calls onFoodMoment when Food Moment button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Food Moment'));
    
    expect(offlineHelpers.onFoodMoment).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
      currentPerson: mockState.auth.personInfo,
    });
  });

  it('calls onCommentMoment when Comment Moment button is pressed', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    const user = userEvent.setup();
    await user.press(screen.getByText('Comment Moment'));
    
    expect(offlineHelpers.onCommentMoment).toHaveBeenCalledWith({
      navigation: expect.any(Object),
      dispatch: mockDispatch,
      currentOrg: mockState.auth.orgInfo,
      currentPerson: mockState.auth.personInfo,
    });
  });

  it('sets navigation params on mount', () => {
    const mockProps = {
      navigation: {
        setParams: jest.fn(),
      },
    };
    
    render(<Offline {...mockProps} />);
    
    expect(mockProps.navigation.setParams).toHaveBeenCalledWith({
      dispatch: mockDispatch,
    });
  });

  it('fetches pending check-ins count on mount', async () => {
    render(<Offline navigation={mockNavigation} />);
    
    await waitFor(() => {
      expect(syncHelpers.getPendingCheckInsCount).toHaveBeenCalled();
    });
  });

  it('renders navigation options correctly', () => {
    const navigationOptions = Offline.navigationOptions({
      navigation: mockNavigation,
      route: mockRoute,
    });
    
    expect(navigationOptions.title).toBe('Offline Mode');
    expect(navigationOptions.headerRight).toBeDefined();
  });

  it('shows alert when headerRight is pressed', () => {
    const navigationOptions = Offline.navigationOptions({
      navigation: mockNavigation,
      route: mockRoute,
    });
    
    const HeaderRight = navigationOptions.headerRight;
    render(<HeaderRight />);
    
    fireEvent.press(screen.getByText('icon'));
    
    expect(Alert.alert).toHaveBeenCalledWith(
      'Go Online',
      'Would you like to sync your offline moments before going online?',
      expect.any(Array),
      { cancelable: false }
    );
  });
});
