import React from 'react';
import { render, fireEvent, act } from '@testing-library/react-native';
import { Provider } from 'react-redux';
import configureStore from 'redux-mock-store';
import thunk from 'redux-thunk';
import AsyncStorage from '@react-native-async-storage/async-storage';
import OfflineCheckIn from '../components/OfflineCheckIn';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn().mockResolvedValue(undefined),
  multiSet: jest.fn().mockResolvedValue(undefined),
}));

// Mock the cacheHelpers
jest.mock('../cacheHelpers', () => ({
  getCachedPeople: jest.fn(),
  updateCachedChildren: jest.fn(),
}));

const mockStore = configureStore([thunk]);

describe('OfflineCheckIn', () => {
  let store;
  let mockNavigation;
  let mockRoute;

  beforeEach(() => {
    store = mockStore({
      auth: {
        personInfo: {
          _id: 'testUserId',
          firstName: 'Test',
          lastName: 'User',
          type: 'staff'
        },
        orgInfo: {
          _id: 'testOrgId',
          name: 'Test Org'
        }
      }
    });

    mockNavigation = {
      navigate: jest.fn(),
      goBack: jest.fn(),
    };

    mockRoute = {
      params: {
        onComplete: jest.fn(),
        selectedPeople: [
          {
            _id: 'child1',
            firstName: 'John',
            lastName: 'Doe',
            name: 'John Doe',
            type: 'person',
            checkedIn: false,
            defaultGroupId: 'group1',
            defaultGroupName: 'Group 1'
          }
        ]
      }
    };

    // Reset all mocks
    jest.clearAllMocks();
    AsyncStorage.setItem.mockResolvedValue(undefined);
    AsyncStorage.getItem.mockResolvedValue(null);
  });

  it('handles check-in process successfully', async () => {
    const { getByText } = render(
      <Provider store={store}>
        <OfflineCheckIn navigation={mockNavigation} route={mockRoute} />
      </Provider>
    );

    // Trigger check-in
    await act(async () => {
      fireEvent.press(getByText('Save'));
    });

    // Wait for actions to complete
    const actions = store.getActions();
    expect(actions[0].type).toBe('OFFLINE_CHECKIN');
    expect(actions[1].type).toBe('OFFLINE_CHECKIN_SUCCESS');
    expect(mockNavigation.goBack).toHaveBeenCalled();
    expect(mockRoute.params.onComplete).toHaveBeenCalled();
  });

  it('handles check-in process with single person', async () => {
    const { getByText } = render(
      <Provider store={store}>
        <OfflineCheckIn navigation={mockNavigation} route={mockRoute} />
      </Provider>
    );

    expect(getByText('John Doe')).toBeTruthy();

    await act(async () => {
      fireEvent.press(getByText('Save'));
    });

    const actions = store.getActions();
    expect(actions[1].type).toBe('OFFLINE_CHECKIN_SUCCESS');
    expect(actions[1].payload).toHaveLength(1);
    expect(actions[1].payload[0].firstName).toBe('John');
  });

  it('handles error during check-in process', async () => {
    AsyncStorage.setItem.mockRejectedValueOnce(new Error('Storage error'));

    const { getByText } = render(
      <Provider store={store}>
        <OfflineCheckIn navigation={mockNavigation} route={mockRoute} />
      </Provider>
    );

    await act(async () => {
      fireEvent.press(getByText('Save'));
    });

    const actions = store.getActions();
    expect(actions[0].type).toBe('OFFLINE_CHECKIN');
    expect(actions[1].type).toBe('OFFLINE_CHECKIN_ERROR');
    expect(mockNavigation.goBack).not.toHaveBeenCalled();
  });
});
