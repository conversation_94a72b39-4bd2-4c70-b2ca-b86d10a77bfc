import Meteor from 'react-native-meteor';
import { getCachedPeople, updateCachedChildren, clearCachedChildren } from '../cacheHelpers';

// Mock Meteor
jest.mock('react-native-meteor', () => ({
  collection: jest.fn()
}));

describe('getCachedPeople', () => {
  let mockCollection;

  beforeEach(() => {
    jest.clearAllMocks();

    // Setup mock collection with initial data
    const mockData = [
      {
        _id: '1',
        firstName: 'Alice',
        lastName: 'Smith',
        type: 'person',
        checkedIn: true,
        checkInGroupId: 'g1',
        checkInGroupName: 'Group 1'
      },
      {
        _id: '2',
        firstName: 'Bob',
        lastName: 'Jones',
        type: 'person',
        checkedIn: false,
        checkInGroupId: 'g2',
        checkInGroupName: 'Group 2'
      }
    ];

    mockCollection = {
      find: jest.fn(query => {
        // If query has checkedIn: false, filter to only return non-checked-in children
        if (query && query.checkedIn === false) {
          return mockData.filter(person => person.checkedIn === false);
        }
        // Otherwise return all data
        return mockData;
      }),
      remove: jest.fn(),
      insert: jest.fn()
    };

    Meteor.collection.mockReturnValue(mockCollection);
  });

  it('returns the correct cached children structure', () => {
    const result = getCachedPeople();
    expect(result).toEqual([
      {
        _id: '1',
        firstName: 'Alice',
        lastName: 'Smith',
        name: 'Alice Smith',
        type: 'person',
        checkedIn: true,
        checkInGroupId: 'g1',
        checkInGroupName: 'Group 1'
      },
      {
        _id: '2',
        firstName: 'Bob',
        lastName: 'Jones',
        name: 'Bob Jones',
        type: 'person',
        checkedIn: false,
        checkInGroupId: 'g2',
        checkInGroupName: 'Group 2'
      }
    ]);
  });

  it('filters out checked-in children when requested', () => {
    const result = getCachedPeople(true);
    expect(result).toEqual([
      {
        _id: '2',
        firstName: 'Bob',
        lastName: 'Jones',
        name: 'Bob Jones',
        type: 'person',
        checkedIn: false,
        checkInGroupId: 'g2',
        checkInGroupName: 'Group 2'
      }
    ]);
  });

  it('handles missing Meteor collection', () => {
    Meteor.collection.mockReturnValue(null);
    const result = getCachedPeople();
    expect(result).toEqual([]);
  });

  it('handles query failure', () => {
    mockCollection.find.mockReturnValue(null);
    const result = getCachedPeople();
    expect(result).toEqual([]);
  });

  it('handles errors gracefully', () => {
    mockCollection.find.mockImplementation(() => {
      throw new Error('Test error');
    });
    const result = getCachedPeople();
    expect(result).toEqual([]);
  });
});
