import Meteor from 'react-native-meteor';
import AsyncStorage from '@react-native-async-storage/async-storage';

export const CACHE_KEY = 'offline_children';

/**
 * Maps a person object to the standard format used throughout the app
 * @param {Object} person - The person object from Meteor collection
 * @returns {Object} Standardized person object
 */
const mapPersonToStandardFormat = (person) => ({
  _id: person._id,
  name: `${person.firstName} ${person.lastName}`,
  firstName: person.firstName,
  lastName: person.lastName,
  checkedIn: person.checkedIn === true,
  checkInGroupId: person.checkInGroupId,
  checkInGroupName: person.checkInGroupName,
  defaultGroupId: person.defaultGroupId,
  defaultGroupName: person.defaultGroupName,
  type: person.type || 'person',
});

/**
 * Gets the Meteor collection for people
 * @returns {Object|null} Meteor collection or null if not available
 */
const getPeopleCollection = () => {
  try {
    const collection = Meteor.collection('people');
    if (!collection) {
      console.warn('People collection not available');
      return null;
    }
    return collection;
  } catch (error) {
    console.error('Error getting people collection:', error);
    return null;
  }
};

/**
 * Retrieves children from the cache
 * @param {boolean} filterCheckedIn - If true, only returns children who are not checked in
 * @returns {Array} Array of children objects
 */
export const getCachedPeople = (filterCheckedIn = false) => {
  try {
    // Try to get from Meteor collection first
    const collection = getPeopleCollection();
    if (!collection) {
      return [];
    }

    let query;
    if (filterCheckedIn) {
      // If filterCheckedIn is true, only return children who are NOT checked in
      query = collection.find({ checkedIn: false });
    } else {
      // Otherwise return all children
      query = collection.find({});
    }

    if (!query) {
      return [];
    }

    return query.map(mapPersonToStandardFormat);
  } catch (error) {
    console.error('Error getting cached children:', error);
    return [];
  }
};

/**
 * Updates the Meteor local collection with new person data
 * @param {Object} collection - Meteor collection
 * @param {Object} personData - Updated person data
 * @returns {boolean} Success status
 */
const updateMeteorCollection = (collection, personData) => {
  try {
    collection.remove({ _id: personData._id });
    collection.insert({ ...personData, type: 'person' });
    return true;
  } catch (error) {
    console.error('Error updating Meteor collection:', error);
    return false;
  }
};

/**
 * Updates children's data in the Meteor collection
 * @param {Array} updates - Array of person objects to update
 * @param {Function} dispatch - Redux dispatch function (optional)
 * @returns {boolean} Success status
 */
export const updateCachedChildren = (updates, dispatch = null) => {
  try {
    const collection = getPeopleCollection();
    if (!collection) return false;

    // Update each child
    for (const update of updates) {
      const personData = {
        ...update,
        type: 'person'
      };

      const success = updateMeteorCollection(collection, personData);
      if (!success) {
        console.warn('Failed to update child:', update._id);
      }
    }

    // If dispatch function is provided, update the Redux state
    if (dispatch) {
      try {
        const { updateOfflinePeople } = require('../../actions/offline');
        dispatch(updateOfflinePeople());
      } catch (e) {
        console.warn('Failed to update offline people state:', e);
      }
    }

    return true;
  } catch (error) {
    console.error('Error updating cached children:', error);
    return false;
  }
};

/**
 * Clears the children cache
 */
export const clearCachedChildren = () => {
  try {
    const collection = getPeopleCollection();
    if (collection) {
      collection.remove({});
    }
  } catch (error) {
    console.error('Error clearing cached children:', error);
  }
};
