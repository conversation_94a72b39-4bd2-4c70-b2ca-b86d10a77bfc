import React, {useEffect, useRef, useState} from 'react';
import colors from '../config/colors.json';
import { Text, TouchableOpacity, Animated, Platform } from 'react-native';
import { useNetwork } from './NetworkContext';
import { FontAwesomeIcon } from "@fortawesome/react-native-fontawesome";
import {useDispatch, useSelector} from "react-redux";
import {toggleOfflineMode, toggleOnlineMode} from "../actions/userData";
import {syncMoments} from "../actions/offline";
import { syncOfflineCheckIns, syncOfflineCheckOuts } from '../screens/Offline/syncHelpers';

const BANNER_HEIGHT = 40;

const OfflineBanner = () => {
  const network = useNetwork();
  const heightAnim = useRef(new Animated.Value(0)).current;

  const dispatch = useDispatch();
  const offlineMode = useSelector(state => state.auth.offlineMode);

  // handleSync is now handled directly in toggleOnlineMode

  const onPress = () => {
    heightAnim.setValue(0);
    if (network.isOffline && !offlineMode) {
      dispatch(toggleOfflineMode());
    } else if (!network.isOffline && offlineMode) {
      // Just use toggleOnlineMode which now handles syncing internally
      dispatch(toggleOnlineMode());
    }
  }

  const opacityAnim = heightAnim.interpolate({
    inputRange: [0, BANNER_HEIGHT],
    outputRange: [0, 1],
    extrapolate: 'clamp'
  });

  useEffect(() => {
    if ((network.isOffline && !offlineMode) || (!network.isOffline && offlineMode)) {
      Animated.timing(heightAnim, {
        toValue: BANNER_HEIGHT,
        duration: 300,
        useNativeDriver: false,
      }).start();
    } else {
      Animated.timing(heightAnim, {
        toValue: 0,
        duration: 300,
        useNativeDriver: false,
      }).start();
    }
  }, [network.isOffline, offlineMode]);

  return (
    <Animated.View
      style={{
        height: heightAnim,
        overflow: 'hidden',
        backgroundColor: colors.secondary,
        width: '100%',
        zIndex: 1,
      }}>
      <Animated.View
        style={{
          opacity: opacityAnim,
          height: BANNER_HEIGHT,
          width: '100%',
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'center',
        }}>
        <TouchableOpacity
          style={{
            width: '100%',
            height: '100%',
            paddingVertical: 8,
            flexDirection: 'row',
            alignItems: 'center',
            justifyContent: 'center',
          }}
          onPress={onPress}>
          <FontAwesomeIcon
            icon={network.isOffline ? 'fa fa-wifi-slash' : 'fa fa-wifi'}
            color={colors.white}
            size={16}
            style={{marginRight: 8}}
          />
          <Text style={{color: colors.white, fontWeight: 'bold'}}>
            {network.isOffline && !offlineMode
              ? 'No Internet. Tap to switch to Offline Mode.'
              : !network.isOffline && offlineMode
                ? 'Online. Tap to sync data and exit Offline Mode.'
                : ''
            }
          </Text>
        </TouchableOpacity>
      </Animated.View>
    </Animated.View>
  );
};

export default OfflineBanner;
