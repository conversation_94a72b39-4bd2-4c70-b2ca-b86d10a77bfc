import { DiscountTypes } from './discountTypes';
import { Meteor } from 'meteor/meteor';
const dot = require('dot-object');
import { MiscUtils } from "./util/miscUtils";
const moment = require('moment-timezone');
import { checkIn } from './checkIn';
import { checkOut } from './checkOut';
import { AvailableCustomizations } from './customizations';
import {
	PLAN_BUNDLE_TYPE,
	SCALED_PLAN_FREQUENCIES,
	PUNCH_CARD_TYPE,
	PLAN_TYPE,
	ITEM_TYPE, PLANS_CHARGED_BY_CHECKINS
} from './constants/billingConstants';
import { archiveCoupon, deleteCoupon, insertCoupon, suspendCoupon, updateCoupon } from './Coupon';
import { RelUtils } from "./util/relUtils";
import { BillingUtils } from './util/billingUtils';
import { InsertBillingChargeService } from './insertBillingChargeService';
import { Log } from './util/log';
import { InsertUpdateAnnouncementsService } from './insertUpdateAnnouncementsService';
import { HistoryAuditChangeTypes, HistoryAuditPeoplePerformedByNames, HistoryAuditRecordTypes } from './constants/historyAuditConstants';
import { MomentUtils } from './util/momentUtils';
import { ScheduleUtils } from './util/scheduleUtils';
import { SSR } from "./util/ssrUtils";
import { Blaze } from "meteor/blaze";
import { ReportsUtilLib } from './util/reportsUtil';
import { People } from './collections/people';
import { Curriculums } from './collections/curriculum';
import { CurriculumTheme, CurriculumThemes } from './collections/curriculumThemes';
import { Announcements } from './collections/announcements';
import { MediaReviews } from './collections/mediaReview';
const AWS = require('aws-sdk');

const SNS = new AWS.SNS({
	region: "us-east-1",
	accessKeyId: Meteor.settings.mpAWSaccessKey,
	secretAccessKey: Meteor.settings.mpAWSsecretKey,
});
import { DateTimeUtils } from "./util/dateTimeUtils";
import logger from "../imports/winston";
import { CurriculumUtils } from './activities/curriculumUtils';
import { Groups } from './collections/groups';
import _ from './util/underscore';
import { processPermissions } from './permissions';
import { Orgs } from './collections/orgs';
import { processAnalytics } from '../server/analytics';
import { Invoices } from './collections/invoices';
import { Relationships } from './collections/relationships';
import { Messages } from './collections/messages';
import { processMessageNotifications } from '../server/processMessageNotifications';
import { UserInvitations } from './collections/userInvitations';
import { processInvitation } from '../server/processInvitation';
import { processSummaryMail2021 } from '../server/emails/v2021/processSummaryMail2021';
import { recalculateGroupMediaDesignation } from './momentHelpers';
import { Moments } from './collections/moments';
import { Foods } from './collections/food';
import { Reservations } from './collections/reservations';
import { MomentDefinitions } from './collections/momentDefinitions';
import { deletePersonUpdateOrDeleteUser, insertPersonCreateUser } from '../server/usersAndPeople';
import { processMomentAndCommonData } from './momentHelpers';
import { processRealtime } from '../server/processRealtime';
import { ChildcareCrmAccounts } from './collections/childcareCrmAccounts';
import { MetaMoments } from './collections/metaMoments';
import { NewMessages } from './collections/newMessages';
import { PeopleDataValidations } from './collections/peopleDataValidation';
import { TimeCards } from './collections/timeCards';
import { getTodaysMissingChildren } from '../server/classList';
import { deleteEndpointArn } from '../server/snsHelper';
import { cancelAnnouncementReminder } from '../server/agenda/agendaScheduler';
import { recalculateGroupDashboard } from '../server/agenda/recalculateGroupDashboard';
import { processPasswordResetMail } from '../server/processPasswordResetMail';
import { factoryReportEmail } from '../server/factoryReportEmail';
import { processDataValidationReminder } from '../server/processDataValidationReminders';
import { processDocumentNotification } from '../server/processDocumentNotification';
import { UserUtils } from './util/usersUtil';
import { PeopleTypes } from './constants/peopleConstants';
import { GroupsService } from '../server/groupsService'


global.myMethodCache = {};

Meteor.methods({
	'validateMoment': async function (momentData) {
		await processPermissions({
			assertions: [{ context: "moments", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!(await currentPerson.hasPermissions("postMoment", momentData.momentType))) {
			throw new Meteor.Error(403, "Insufficient permissions");
		}

		//is this a new or old moment?
		var editMomentStatus = momentData["_id"] ? true : false;

		//process common elements
		var processedObj = await processMomentAndCommonData(momentData, editMomentStatus);
		var commonData = processedObj.commonData;
		momentData = processedObj.momentData;

		await MomentUtils.processTaggedPeople(momentData, editMomentStatus, commonData, true);
		return true;
	},
	'insertMoment': async function (momentsData) {
		await processPermissions({
			assertions: [{ context: "moments", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		const currentPerson = (currentUser) ? await currentUser?.fetchPerson() : null;
		const org = await Orgs.current();
		if (!currentPerson || ! await currentPerson.hasPermissions("postMoment", momentsData.momentType)) {
			throw new Meteor.Error(403, "Insufficient permissions");
		}
		// if checkins are required and user is not checked in throw error
		if (org && org.hasCustomization("moments/staffRequiredCheckin/enabled") && !currentPerson.checkedIn) {
			throw new Meteor.Error(403, "Staff and admins must be checked in to post moments. Please visit your profile to checkin.");
		}
		//is this a new or old moment?
		var editMomentStatus = momentsData["_id"] ? true : false;
		var timezone = org.getTimezone();
		//process common elements
		var processedObj = await processMomentAndCommonData(momentsData, editMomentStatus);
		var commonData = processedObj.commonData;
		var momentData = processedObj.momentData;

		var processResults = await MomentUtils.processTaggedPeople(momentData, editMomentStatus, commonData, false);
		var taggedPeople = processResults.taggedPeople;
		var suppressRealtime = editMomentStatus;

		switch (momentData.momentType) {
			case "potty":
				commonData.pottyType = momentData.pottyType;
				commonData.momentTypePretty = "Potty";
				if (org.hasCustomization("moments/potty/showContinence"))
					commonData.pottyTypeContinence = momentData.pottyTypeContinence;
				commonData.pottyTraining = momentData.pottyTraining;
				commonData.pottyAppliedOintment = momentData.pottyAppliedOintment;
				break;
			case "food":
				commonData.foodType = momentData.foodType;
				commonData.foodAmount = momentData.foodAmount;
				if (momentData.foodItems)
					commonData.foodItems = momentData.foodItems;
				if (org.hasCustomization("moments/food/showPercentage"))
					commonData.foodAmountPercent = momentData.foodAmountPercent;
				if (commonData.foodType == "Bottle") {
					commonData.foodBottleAmountBreastmilkOffered = momentData.foodBottleAmountBreastmilkOffered;
					commonData.foodBottleAmountBreastmilkConsumed = momentData.foodBottleAmountBreastmilkConsumed;
					commonData.foodBottleAmountFormulaOffered = momentData.foodBottleAmountFormulaOffered;
					commonData.foodBottleAmountFormulaConsumed = momentData.foodBottleAmountFormulaConsumed;
					commonData.foodBottleAmountMilkOffered = momentData.foodBottleAmountMilkOffered;
					commonData.foodBottleAmountMilkConsumed = momentData.foodBottleAmountMilkConsumed;
				}
				if (commonData.foodType == "Baby Food") {
					commonData.foodBabyFoodType = momentData.foodBabyFoodType;
					commonData.foodBottleAmountBabyFoodConsumed = momentData.foodBottleAmountBabyFoodConsumed;
					commonData.foodBottleAmountBabyFoodConsumedMeasure = org.foodUnits('babyFood');
				}
				if (commonData.foodType == "Cereal") {
					commonData.foodBottleAmountCerealConsumed = momentData.foodBottleAmountCerealConsumed;
					commonData.foodBottleAmountCerealConsumedMeasure = org.foodUnits('cereal');
				}
				if (commonData.foodType == "Tube")
					commonData.foodTubeAmount = momentData.foodTubeAmount;
				commonData.momentTypePretty = "Food";
				break;
			case "sleep":
				if (momentData.endTime) {
					commonData.endTime = momentData.endTime;
					if (momentData["_id"]) suppressRealtime = false;
				} else {
					suppressRealtime = true;
				}

				if (org.hasCustomization("moments/sleep/distressedCheck")) {
					commonData.distressedSleepCheck = true;
				}

				if (momentData.sleepCheckInterval) commonData.sleepCheckInterval = momentData.sleepCheckInterval;
				commonData.momentTypePretty = "Sleep";
				commonData.sleepDidNotSleep = momentData.sleepDidNotSleep;
				commonData.sleepSatQuietly = momentData.sleepSatQuietly;
				break;
			case "activity":
				commonData.activityType = momentData.activityType;
				commonData.activityEngagement = momentData.activityEngagement;
				commonData.momentTypePretty = "Activity";
				break;
			case "supplies":
				commonData.supplyType = Array.isArray(momentData.supplyType) ? momentData.supplyType.join(",") : momentData.supplyType;
				commonData.supplyTypeRaw = momentData.supplyType;
				commonData.momentTypePretty = "Supplies";
				break;
			case "medical":
				const medicalAdministeredById = momentData.medicalAdministeredBy,
					medicalAdministeredByPersonRaw = medicalAdministeredById && await People.findOneAsync({ orgId: org._id, _id: medicalAdministeredById }),
					medicalAdministeredByPerson = medicalAdministeredByPersonRaw && {
						fullName: medicalAdministeredByPersonRaw.firstName + " " + medicalAdministeredByPersonRaw.lastName,
						firstName: medicalAdministeredByPersonRaw.firstName,
						lastName: medicalAdministeredByPersonRaw.lastName
					};
				commonData.medicalAdministeredBy = medicalAdministeredById;
				commonData.medicalAdministeredByPerson = medicalAdministeredByPerson;
				commonData.medicalDoctorName = momentData.medicalDoctorName;
				commonData.medicalMedicationName = momentData.medicalMedicationName;
				commonData.medicalMedicineType = momentData.medicalMedicineType;
				commonData.medicalMedicineAmount = momentData.medicalMedicineAmount;
				commonData.momentTypePretty = "Medical";
				if (org.hasCustomization("moments/medical/useProfileMedications")) {
					if (taggedPeople.length != 1) throw new Meteor.Error(500, "Only one person can be tagged in a medical moment.");
					if (!momentData.medicalMedicationId) throw new Meteor.Error(500, "You must select a medication for this person before posting.");

					const matchedPerson = await People.findOneAsync(taggedPeople[0]);
					const currentMedications = matchedPerson?.currentMedications() ?? [];
					const matchedMedication = _.find(currentMedications, (me) => (me._id == momentData.medicalMedicationId || me.salesforceRecordId == momentData.medicalMedicationId));

					if (!matchedMedication) {
						throw new Meteor.Error(500, "Could not find matching medication for tagged person.");
					}

					commonData.medicalMedicationId = momentData.medicalMedicationId;
					commonData.medicalMedication = matchedMedication;
				}
				break;
			case "mood":
				commonData.moodLevel = momentData.moodLevel;
				commonData.momentTypePretty = "Mood";
				break;
			case "comment":
				commonData.momentTypePretty = "Comment";
				break;
			case "prospect":
				commonData.momentTypePretty = "Prospect";
				commonData.prospectType = momentData.prospectType;
				break;
			case "alert":
				commonData.momentTypePretty = "Notification";
				commonData.alertSendTypeEmail = momentData.alertSendTypeEmail;
				commonData.alertSendTypeText = momentData.alertSendTypeText;
				commonData.alertSendTypePush = momentData.alertSendTypePush;
				if (!(commonData.alertSendTypeEmail || commonData.alertSendTypeText || commonData.alertSendTypePush)) {
					commonData.alertSendTypeEmail = true; commonData.alertSendTypeText = true;
				}

				if (commonData.alertSendTypeText && !suppressRealtime) {
					commonData.smsAlert = momentData.smsAlert;
				}
				commonData.alertShowCheckin = momentData.alertShowCheckin;
				break;
			case "learning":
				commonData.momentTypePretty = "Learning";
				commonData.learningType = momentData.learningType;
				commonData.learningCurriculumId = momentData.learningCurriculumId;
				if (commonData.learningCurriculumId) {
					const curriculum = await Curriculums.findOneAsync(commonData.learningCurriculumId);
					if (curriculum) commonData.learningCurriculum = curriculum;
				}
				commonData.learningAssessmentLevel = momentData.learningAssessmentLevel;
				break;
			case "portfolio":
				commonData.momentTypePretty = "Portfolio";
				if (momentData.portfolioCurriculumId) commonData.portfolioCurriculumId = momentData.portfolioCurriculumId;
				if (commonData.portfolioCurriculumId) {
					const curriculum = await Curriculums.findOneAsync(commonData.portfolioCurriculumId);
					if (curriculum) commonData.portfolioCurriculum = curriculum;
				}
				commonData.portfolioAssessments = momentData.portfolioAssessments;
				break;
			case "incident":
				commonData.momentTypePretty = "Incident";
				commonData.incidentNature = momentData.incidentNature;
				commonData.incidentActionTaken = momentData.incidentActionTaken;
				commonData.incidentLocation = momentData.incidentLocation;
				break;
			case "illness":
				commonData.momentTypePretty = "Illness";
				commonData.illnessSymptoms = Array.isArray(momentData.illnessSymptoms) ? momentData.illnessSymptoms : [momentData.illnessSymptoms];
				break;
			case "ouch":
				commonData.momentTypePretty = "Ouch";
				commonData.ouchDescription = momentData.ouchDescription;
				commonData.ouchCare = momentData.ouchCare;
				commonData.ouchContactedParent = momentData.ouchContactedParent;
				commonData.ouchCalledParentTime = momentData.ouchCalledParentTime;
				commonData.ouchContactedDoctor = momentData.ouchContactedDoctor;
				commonData.ouchCalledDoctorTime = momentData.ouchCalledDoctorTime;
				commonData.ouchNurseNotified = momentData.ouchNurseNotified;
				commonData.ouchProfessionalMedication = momentData.ouchProfessionalMedication;
				break;
			case "checkin":
				if (org.hasCustomization("moments/checkin/showTransportation"))
					commonData.checkInTransportation = momentData.checkInTransportation;

				break;
			case "checkout":
				if (org.hasCustomization("moments/checkout/showTransportation"))
					commonData.checkOutTransportation = momentData.checkOutTransportation;
				break;
			case "move":
				break;
			default:
				await MomentUtils.setDynamicMomentTypeData(momentData.momentType, momentData, commonData);
		}
		Log.debug("insertMoment", momentData);

		//handle modified check in moment
		if (editMomentStatus && (momentData.momentType == "checkin" || momentData.momentType == "checkout")) {
			// _.each(taggedPeople, function (pid) {
			Promise.all(
				taggedPeople.map(pid => {
					new Promise(async (resolve, reject) => {
						var curPerson = await People.findOneAsync({ _id: pid });
						if (currentPerson.type == "staff"
							&& (org.hasCustomization("moments/checkin/staffLockdown") || org.hasCustomization("people/staffRequiredPinCodeCheckin/enabled"))
							&& editMomentStatus && (curPerson.type == "staff" || curPerson.type == "admin")) {
							reject({ code: 403, reason: "Access denied -- staff cannot modify checkin/checkout for staff" })
						}

						if (curPerson && curPerson.checkedIn && momentData.momentType == "checkin") {
							var lastCheckInMoment = await Moments.findOneAsync({ momentType: "checkin", taggedPeople: pid }, { sort: { sortStamp: -1 } });

							if (lastCheckInMoment && lastCheckInMoment._id == momentData["_id"]) {
								resolve(await People.updateAsync({ _id: pid }, { "$set": { checkedInOutTime: commonData.sortStamp } }));
							}
						}
					})
				})
			).catch((err) => { throw new Meteor.Error(err.code, err.reason) })
		}
		//handle last moment updates for edit and new
		async function handleLastMoment(lastMoment, momentId) {
			let newMomentPersonUpdate = {
				"$set": {
					"lastInteractionDate": lastMoment.sortStamp,
					"lastMoment": lastMoment
				}
			};
			const byTypeKey = "lastMomentByType." + lastMoment.momentType,
				byTypeSortstampKey = byTypeKey + ".sortStamp",
				byTypeIdKey = byTypeKey + "._id";
			let queryExists = {}, queryTimestamp = {}, querySameMoment = {};
			queryExists[byTypeSortstampKey] = { "$exists": 0 };
			queryTimestamp[byTypeSortstampKey] = { "$lt": lastMoment.sortStamp };

			let query = {
				"_id": { "$in": lastMoment.taggedPeople },
				"$or": [queryExists, queryTimestamp]
			};
			if (momentId) {
				query["$or"][byTypeKey + "._id"] = momentId;
				querySameMoment[byTypeIdKey] = momentId;
				query["$or"].push(querySameMoment);
				lastMoment._id = momentId;
			}
			newMomentPersonUpdate["$set"][byTypeKey] = lastMoment;

			await People.updateAsync(query,
				newMomentPersonUpdate, { multi: true }
			);
		}
		//switch for new versus old moment
		var id, hasMediaAttachments = false, trackingData;
		const mediaReviewOrgWide = org.mediaRequirement?.mediaReviewRequired;
		let mediaReviewFlag = await Groups.findOneAsync(currentPerson.checkInGroupId);
		// ok so if you're not in a current group then check all people;
		if (!mediaReviewFlag) {
			mediaReviewFlag = { orgId: org._id, mediaReviewRequired: processResults.individualMediaReviewRequired };
		}
		// If the org requires media review, require it regardless of group or individual settings
		if (mediaReviewOrgWide === 'Yes') {
			mediaReviewFlag.mediaReviewRequired = true;
		}

		if (editMomentStatus) {
			id = momentData["_id"];
			var existingMoment = await Moments.findOneAsync(id);
			var momentUpdateData = commonData;
			var attributionPerson = (commonData.attributionPersonId) ? await People.findOneAsync({ _id: commonData.attributionPersonId }) : await People.findOneAsync({ _id: existingMoment.createdByPersonId });
			if (attributionPerson) momentUpdateData.attributionName = `${attributionPerson.firstName} ${attributionPerson.lastName}`;

			momentUpdateData.modifiedByName = `${currentPerson.firstName} ${currentPerson.lastName}`;
			momentUpdateData.modifiedBy = currentPerson._id;
			momentUpdateData.comment = momentData.comment;
			momentUpdateData.taggedPeople = _.uniq(taggedPeople);
			await Moments.updateAsync(id, { $set: momentUpdateData });

			if (momentData.mediaFiles && momentData.mediaFiles.length > 0) {
				var addlMediaFiles = [];
				_.each(momentData.mediaFiles, function (f) {
					var newFile = {
						mediaUrl: f.mediaUrl,
						mediaToken: f.mediaToken,
						mediaFileType: f.mediaFileType,
						mediaPath: f.mediaPath
					};
					addlMediaFiles.push(newFile);
				});
				if (addlMediaFiles.length > 0) {
					if (mediaReviewFlag?.mediaReviewRequired) {
						await MediaReviews.insertAsync({
							orgId: mediaReviewFlag?.orgId,
							momentId: id,
							mediaFiles: addlMediaFiles,
							createdBy: currentUser._id
						})
					} else {
						await Moments.updateAsync(id, { $push: { mediaFiles: { $each: addlMediaFiles } } });
					}
					hasMediaAttachments = true;
				}
			}
			if (Meteor.isServer) { Meteor.defer(async function () { await handleLastMoment(momentUpdateData, id); }) };
		} else {
			let mediaId = null;
			var newMomentData = commonData;
			newMomentData.createdAt = new Date().valueOf();
			newMomentData.createdBy = currentUser._id;
			var attributionPerson = (commonData.attributionPersonId) ? await People.findOneAsync({ _id: commonData.attributionPersonId }) : await currentUser?.fetchPerson();
			newMomentData.attributionName = `${attributionPerson.firstName} ${attributionPerson.lastName}`;
			newMomentData.createdByPersonId = currentPerson._id;
			newMomentData.orgId = currentUser["orgId"];
			newMomentData.taggedPeople = _.uniq(taggedPeople);
			const getCheckInGroupId = await Groups.findOneAsync({ _id: currentPerson.checkInGroupId, orgId: org._id });
			const getPreviousClassroomGroupId = await Groups.findOneAsync({ _id: currentPerson.previousClassroomGroupId, orgId: org._id });
			const currentGroup = currentPerson.checkInGroupId ? getCheckInGroupId : null,
				previousClassroomGroup = currentPerson.previousClassroomGroupId ? getPreviousClassroomGroupId : null;
			let currentPersonGroupId = currentGroup?._id || previousClassroomGroup?._id;
			if (previousClassroomGroup) {
				currentPersonGroupId = (currentGroup?.includeClassList) ? currentGroup._id : previousClassroomGroup?._id
			}
			newMomentData.createdByPersonGroupId = currentPersonGroupId;
			newMomentData.createdByPersonCheckInGroupId = currentGroup?._id;

			if (momentData.mediaFiles && momentData.mediaFiles.length > 0) {
				newMomentData.mediaFiles = [];
				_.each(momentData.mediaFiles, function (f) {
					var newFile = {
						mediaUrl: f.mediaUrl,
						mediaToken: f.mediaToken,
						mediaFileType: f.mediaFileType,
						mediaPath: f.mediaPath,
						pending: f.queued || (f.evaporateDirect && f.status == "uploading"),
						fileName: f.name
					};
					newMomentData.mediaFiles.push(newFile);
					hasMediaAttachments = true;
					if (f.queued) suppressRealtime = true;
				});

				if (newMomentData.mediaFiles.length > 0 && mediaReviewFlag?.mediaReviewRequired) {
					mediaId = await MediaReviews.insertAsync({
						orgId: mediaReviewFlag?.orgId,
						mediaFiles: newMomentData.mediaFiles
					});
					newMomentData.mediaFiles = []
				}
			}

			id = await Moments.insertAsync(newMomentData);
			newMomentData._id = id;
			if (mediaId) await MediaReviews.updateAsync(mediaId, { $set: { momentId: id, createdBy: currentUser._id } });

			await org.updateMetric("momentsPosted", 1);

			trackingData = newMomentData;
			trackingData.orgName = org.name;

			if (Meteor.isServer) { Meteor.defer(async function () { await handleLastMoment(newMomentData); }) };
		}
		if (commonData.sortStamp && commonData.sortStamp < new moment().tz(timezone).startOf('day').valueOf())
			suppressRealtime = true;
		if (Meteor.isServer) {
			var currentUserId = (await Meteor.userAsync())._id;

			if (!suppressRealtime) processRealtime(id);

			Promise.all(
				taggedPeople
					.filter((value, index, array) => array.indexOf(value) === index)
					.map(t => {
						new Promise(async (resolve, reject) => {
							const currentPerson = await currentUser?.fetchPerson();
							resolve(
								processAnalytics({
									engagementData: {
										orgId: currentUser.orgId,
										type: "app",
										subType: "created_moment",
										detail: momentData.momentType + (hasMediaAttachments ? "_with_media" : ""),
										momentId: id,
										createdBy: currentPerson._id,
										createdAt: new Date().valueOf(),
										targetPersonId: t,
										sourcePersonId: currentPerson._id
									}
								})
							)
						}).catch((err) => { throw new Meteor.Error(err.code, err.reason) })
					})
			).catch((err) => { throw new Meteor.Error(err.code, err.reason) })

			if (trackingData) {
				processAnalytics({ metaCxData: { type: "created-moment", data: trackingData } });
				if (trackingData?.mediaFiles?.length > 0) processAnalytics({ metaCxData: { type: "moment-with-media", data: trackingData } });
			}
		}
		return id;
	},
	'approveMedia': async function (mediaId) {
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson?.type != "admin") {
			throw new Meteor.Error(403, "Access denied");
		};

		const mediaReview = await MediaReviews.findOneAsync({ _id: mediaId, orgId: currentPerson.orgId });
		if (!mediaReview) throw new Meteor.Error(404, "Not Found");

		await Moments.updateAsync({ _id: mediaReview.momentId }, { $push: { mediaFiles: { $each: mediaReview.mediaFiles } } });
		await MediaReviews.removeAsync(mediaReview._id);
		return true;
	},
	'rejectMedia': async function (mediaId) {
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || currentPerson?.type != "admin") {
			throw new Meteor.Error(403, "Access denied");
		};
		await MediaReviews.removeAsync(mediaId);
		return true;
	},
	'getRecentMoveMomentByGroupId': async function (groupId) {
		this.unblock();
		return;
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || !(currentPerson?.type == "admin" || currentPerson?.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}

		const curTimestamp = Date.now(), cacheKey = `getRecentMoveMomentByGroupId|${(await Meteor.userAsync())._id}|${groupId}`;
		if (myMethodCache[cacheKey] && myMethodCache[cacheKey]["timestamp"] > (curTimestamp - 10000)) {
			return myMethodCache[cacheKey]["data"];
		}

		var recentMove = Moments.findOne(
			{
				orgId: currentPerson.orgId, momentType: "move", $or: [
					{ checkInGroupId: groupId },
					{ checkOutGroupId: groupId }
				]
			},
			{ sort: { createdAt: -1 } }
		);

		myMethodCache[cacheKey] = {
			timestamp: curTimestamp,
			data: recentMove
		}

		return recentMove;
	},
	'getRecentNameToFaceMomentByGroupId': async function (groupId) {
		this.unblock();
		return;
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentPerson || !(currentPerson?.type == "admin" || currentPerson?.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}

		const curTimestamp = Date.now(), cacheKey = `getRecentNameToFaceMomentByGroupId|${(await Meteor.userAsync())?._id}|${groupId}`;
		if (myMethodCache[cacheKey] && myMethodCache[cacheKey]["timestamp"] > (curTimestamp - 10000)) {
			return myMethodCache[cacheKey]["data"];
		}

		var recentNameToFace = Moments.findOne(
			{ orgId: currentPerson.orgId, momentType: "nameToFace", nameToFaceGroupId: groupId },
			{ sort: { createdAt: -1 }, readPreference: "secondaryPreferred" }
		);
		myMethodCache[cacheKey] = {
			timestamp: curTimestamp,
			data: recentNameToFace
		}

		return recentNameToFace;
	},
	'insertMetaMoment': async function (momentData) {
		var currentUser = await Meteor.userAsync();
		var userPerson = await currentUser?.fetchPerson();
		if (!currentUser || !(userPerson.type == "admin" || userPerson.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}
		if (!await userPerson.hasPermissions("postMoment", momentData.momentType)) {
			throw new Meteor.Error(403, "Insufficient permissions");
		}

		const group = await Groups.findOneAsync({ _id: userPerson.checkInGroupId || userPerson.defaultGroupId });
		if (!group) {
			throw new Meteor.Error(500, "You are currently not checked into a group. Please checkin.");
		}

		//process common elements
		var commonData = {
			active: true,
			createdBy: currentUser._id,
			defaultGroupId: group._id,
		};

		var org = await Orgs.current();
		var timezone = org.getTimezone();
		commonData.momentType = momentData.momentType;
		// if (momentData.comment) commonData.comment = momentData.comment;
		if (momentData.time) {
			commonData.time = momentData.time;
		} else {
			commonData.time = new moment().tz(timezone).format("h:mm a");
		}

		if (momentData.date) commonData.date = momentData.date;

		if (!commonData.date) {
			commonData.date = new moment().tz(timezone).format("MM/DD/YYYY");
		}

		if (commonData.date && commonData.time) {
			commonData.sortStamp = new moment.tz(commonData.date + " " + commonData.time, "MM/DD/YYYY h:mm a", timezone).valueOf();
		}

		if (!momentData.taggedPeople) throw new Meteor.Error(500, "At least one person must be tagged.");

		var taggedPeople = [];

		for (let tag of momentData.taggedPeople) {
			var tagType = tag.split("|")[0], tagValue = tag.split("|")[1];
			if (tagType == "group") {
				let peepQuery = {
					type: "person",
					inActive: { $ne: true },
					orgId: (await Meteor.userAsync())["orgId"],
					checkedIn: true,
					checkInGroupId: tagValue
				};
				var peeps = People.find(peepQuery);
				peeps.forEach(function (p) { taggedPeople.push(p._id); });
			}
			else if (tagType == "org") {
				let peepQuery = {
					type: { "$in": ["person", "admin", "staff"] },
					inActive: { $ne: true },
					orgId: (await Meteor.userAsync())["orgId"],
					checkedIn: true,
				};
				var peeps = People.find(peepQuery);
				peeps.forEach(function (p) { taggedPeople.push(p._id); });
			} else if (tagType == "role") {
				throw new Meteor.Error(500, "Cannot tag All Staff in sleep moment");
			}
			else {
				var peep = await People.findOneAsync({ orgId: (await Meteor.userAsync())["orgId"], _id: tagValue });

				if (commonData.sortStamp
					&& commonData.sortStamp > new moment().tz(timezone).startOf("day").valueOf()
					&& peep && peep.type != "prospect" && !peep.checkedIn && !_.contains(["alert", "portfolio"], momentData.momentType))
					throw new Meteor.Error(500, peep.firstName + ' ' + peep.lastName + ' must be checked in to post a moment for today.');
				taggedPeople.push(tagValue);
			}
		}
		if (taggedPeople.length == 0) throw new Meteor.Error(500, "At least one person must be tagged.");

		switch (momentData.momentType) {
			case 'sleep':
				if (momentData.endTime) {
					commonData.active = false;
				}
				momentData.sleepCheckInterval = parseInt(group.sleepCheckInterval || 0);
				commonData.orderChildrenBy = { sleepLastSleepCheck: 1 };
				commonData.momentTypePretty = "Sleep";
				break;
			default:
				throw new Meteor.Error(500, "Invalid meta moment type");
		}

		commonData.orgId = currentUser["orgId"];
		commonData.moments = [];
		const metaId = await MetaMoments.insertAsync(commonData);
		if (metaId) {
			momentData.metaMomentId = metaId;
		} else {
			throw new Meteor.Error(500, "Meta Moment did not create");
		}


		let ids = [];
		for (const p of taggedPeople) {
			const mData = { ...momentData, taggedPeople: [`person|${p}`] };
			const id = await Meteor.callAsync("insertMoment", mData);
			if (id) ids.push(id);
		}

		if (ids.length > 0) {
			await MetaMoments.updateAsync({ _id: metaId }, { $set: { moments: ids, trackedMoments: ids } });
		} else {
			await MetaMoments.updateAsync({ _id: metaId }, { $set: { active: false } });
		}

	},
	'metaMomentEndSleep': async function (opts) {
		var currentUser = await Meteor.userAsync();
		var userPerson = await currentUser?.fetchPerson();
		if (!currentUser || !(userPerson.type == "admin" || userPerson.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}
		var options = {
			endTime: opts.endTime,
		}
		if (opts.sleepDidNotSleep) options.sleepDidNotSleep = true;
		if (opts.sleepSatQuietly) options.sleepSatQuietly = true;

		await Moments.updateAsync({ _id: opts.id }, { $set: options });
	},
	'metaMomentSleepCheck': async function (momentId, opts = {}) {
		var currentUser = await Meteor.userAsync();
		var userPerson = await currentUser?.fetchPerson();
		if (!currentUser || !(userPerson.type == "admin" || userPerson.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}

		const moment = await Moments.findOneAsync({ _id: momentId });
		const createdAt = new Date().valueOf();

		const sleepCheck = {
			personId: moment.taggedPeople[0],
			createdAt,
			createdBy: userPerson._id
		}

		if (opts?.sleepPosition) {
			sleepCheck.sleepPosition = opts.sleepPosition
		}

		if (opts?.distressedSleep) {
			sleepCheck.distressedSleep = opts.distressedSleep
		}

		await Moments.updateAsync({ _id: momentId }, { $addToSet: { sleepChecks: sleepCheck }, $set: { sleepLastSleepCheck: createdAt } });
	},
	'getDataValidations': async function () {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		var userPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser || !userPerson) {
			throw new Meteor.Error(403, "Access denied");
		}

		const dataValidations = await PeopleDataValidations.find({
			orgId: userPerson.orgId,
			active: true,
			peopleIds: { $in: [userPerson._id] },
			"responses.personId": { $nin: [userPerson._id] },
		}).fetchAsync();

		return { dataValidations };
	},
	'saveDataValidation': async function (data) {
		var currentUser = await Meteor.userAsync();
		var userPerson = currentUser && await currentUser.fetchPerson();

		if (!currentUser || !userPerson) {
			throw new Meteor.Error(403, "Access denied");
		}

		if (data.personId != userPerson._id) {
			throw new Meteor.Error(403, "Not Allowed");
		}

		const org = await Orgs.current();
		const responseTime = new Date().valueOf();
		const personResponse = {
			personId: userPerson._id,
			createdAt: responseTime,
		};
		const personProfileUpdate = {};
		const pdv = await PeopleDataValidations.findOneAsync({ _id: data._id, orgId: userPerson.orgId });

		const prefix = (org.profileDataPrefix()) ? `${org.profileDataPrefix()}.` : "";
		for (let [key, val] of Object.entries(data.state)) {
			personResponse[key] = val.value
			personProfileUpdate[`${prefix}${key}`] = val.value;
		}

		if (pdv.reportProfileFields) {
			for (const field of pdv.reportProfileFields) {
				personResponse[field] = dot.pick(`${prefix}${field}`, userPerson)
			}
		}

		if (pdv.childReportProfileFields) {
			personResponse.childReportProfileFields = [];
			const perCursor = userPerson.findInheritedRelationships();
			const peopleRelationshipList = await perCursor.fetchAsync();
			for (const relationship of peopleRelationshipList) {
				const targetPerson = await relationship.fetchTargetPerson();
				if (targetPerson) {
					const fieldObj = {
						name: `${targetPerson.firstName} ${targetPerson.lastName}`
					};
					for (const field of pdv.childReportProfileFields) {
						fieldObj[field] = dot.pick(`${prefix}${field}`, targetPerson);
					}
					personResponse.childReportProfileFields.push(dot.object(fieldObj));
				}
			}

		}

		dot.object(personResponse);

		await People.updateAsync({ _id: userPerson._id }, { $set: personProfileUpdate });
		await PeopleDataValidations.updateAsync({ _id: data._id, orgId: userPerson.orgId }, { $addToSet: { responses: personResponse }, $set: { lastResponseTime: responseTime } });

	},
	'insertGroup': async function (groupData) {
		await processPermissions({
			assertions: [{ context: "groups", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		check(groupData.groupName, String);
		if (groupData.capacity) check(groupData.capacity, String);
		if (groupData.preferredCapacity) check(groupData.preferredCapacity, String);
		if (groupData.ratio) check(groupData.ratio, String);

		if (groupData.groupName.length == 0) throw new Meteor.Error(500, "Name cannot be empty");
		let insertData = { name: groupData.groupName, capacity: groupData.capacity, createdBy: currentUser._id, orgId: currentUser["orgId"] };
		if (groupData.typeInfant) insertData.typeInfant = true;
		const groupId =  await Groups.insertAsync(insertData);
		const orgId = currentUser["orgId"];
		await GroupsService.setScheduleTypeToPlans(orgId, groupId);
	},
	'updateGroup': async function (groupData) {
		await processPermissions({
			assertions: [{ context: "groups", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const org = await Orgs.current();

		var groupTarget = await Groups.findOneAsync(groupData.groupId);
		if (!(groupTarget.orgId == (await Meteor.userAsync())["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		var updateData = {};
		if (groupData.hasOwnProperty('name')) {
			check(groupData.name, String);
			updateData.name = groupData.name;
		}
		if (groupData.hasOwnProperty('capacity')) {
			check(groupData.capacity, String);
			updateData.capacity = groupData.capacity;
		}
		if (groupData.sleepCheckInterval) {
			check(groupData.sleepCheckInterval, String);
			updateData.sleepCheckInterval = groupData.sleepCheckInterval;
		}
		if (groupData.preferredCapacity) {
			check(groupData.preferredCapacity, String);
			updateData.preferredCapacity = groupData.preferredCapacity;
		}
		if (groupData.enrollmentGoal) {
			check(groupData.enrollmentGoal, String);
			updateData.enrollmentGoal = groupData.enrollmentGoal;
		}
		if (groupData.ratio) {
			check(groupData.ratio, String);
			updateData.ratio = groupData.ratio;
		}
		if (groupData.ageGroup) {
			updateData.ageGroup = groupData.ageGroup;
		}
		if (groupData.hasOwnProperty('includeClassList')) {
			updateData.includeClassList = groupData.includeClassList;
		}
		if (groupData.hasOwnProperty('activitiesAgeGroup')) {
			check(groupData.activitiesAgeGroup, String);
			updateData.activitiesAgeGroup = groupData.activitiesAgeGroup;
		}
		if (groupData.type && _.contains(org.getGroupTypes(), groupData.type)) {
			updateData.groupType = groupData.type;
		}

		if (org.isChildCare()) {
			if (groupData.typeInfant == true) {
				updateData.groupType = "Infant";
			}
			updateData.typeInfant = updateData.groupType == "Infant";
		}

		await Groups.updateAsync(groupData.groupId, { $set: updateData });
	},
	'deleteGroup': async function (groupId) {
		await processPermissions({
			assertions: [{ context: "groups", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		var groupTarget = await Groups.findOneAsync(groupId);
		if (!(groupTarget.orgId == (await Meteor.userAsync())["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		await Groups.removeAsync(groupId);
	},
	'getOrgGroups': async function (orgId) {
		if (!orgId) {
			throw new Meteor.Error('500', "Invalid orgId");
		}

		return await Groups.find({ orgId: orgId }).fetchAsync();
	},
	'insertGroupSuggestionRule': async function (groupId, values) {
		var currentUser = await Meteor.userAsync();
		const userPerson = await currentUser?.fetchPerson();
		var groupTarget = await Groups.findOneAsync(groupId);
		if (!currentUser ||
			!(userPerson.type == "admin") ||
			!(groupTarget.orgId == (await Meteor.userAsync())["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		if (values.ruleType == "media") {
			var insertData = {
				mediaNumber: parseInt(values.mediaNumber) || null,
				mediaAbsenceDays: parseInt(values.mediaAbsenceDays) || null,
				mediaDays: parseInt(values.thresholdDays) || null
			};
			await Groups.updateAsync({ _id: groupId }, { $set: { mediaRequirement: insertData } });
			if (Meteor.isServer) {
				//it should be async
				recalculateGroupDashboard(groupId)
			}
		} else {
			check(values.momentType, String);
			check(values.threshold, String);
			if (groupTarget.groupSuggestionRules && _.find(groupTarget.groupSuggestionRules, function (r) { return r.momentType == values.momentType; }))
				throw new Meteor.Error(500, "A suggestion rule already exists for that moment type");

			var insertData = {
				momentType: values.momentType,
				// thresholdHours: values.thresholdType == "days" ? 24 * parseInt(values.threshold) : parseInt(values.threshold)
				thresholdHours: values.thresholdType == "days" ? 24 * parseFloat(values.threshold) : parseFloat(values.threshold)

			};
			await Groups.updateAsync({ _id: groupId }, { $push: { groupSuggestionRules: insertData } });
			if (Meteor.isServer) {
				//This should run async
				recalculateGroupDashboard(groupId);
			}
		}

	},
	'deleteGroupSuggestionRule': async function (groupId, momentType) {
		var currentUser = await Meteor.userAsync();
		const userPerson = await currentUser?.fetchPerson();
		var groupTarget = await Groups.findOneAsync(groupId);
		if (!currentUser ||
			!(userPerson.type == "admin") ||
			!(groupTarget.orgId == (await Meteor.userAsync())["orgId"]))
			throw new Meteor.Error(403, "Access denied");
		check(momentType, String);
		if (momentType == "mediaRequirement") {
			await Groups.updateAsync({ _id: groupId }, { $unset: { mediaRequirement: 1 } });
		} else {
			await Groups.updateAsync({ _id: groupId }, { $pull: { groupSuggestionRules: { "momentType": momentType } } });
		}
		if (Meteor.isServer) {
			//this should be async
			recalculateGroupDashboard(groupId)
		}
	},
	'removeWaitListAdded': async function (personId) {
		await processPermissions({
			assertions: [{ context: "people/addModifyUsers", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		await People.updateAsync({ _id: personId }, { $unset: { waitlistAddedDate: 1 } });
		return true;
	},
	'removeDesignations': async function (personId) {
		await processPermissions({
			assertions: [{ context: "people/addModifyUsers", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		await People.updateAsync({ _id: personId }, { $set: { designations: [] } });
		return true;
	},
	'insertPerson': async function (personData, orgId = null, fromRegistration = false, regId = null) {
		let historyPerformedByName = null;
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "people/addModifyUsers", action: "edit" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
		} else if (regId || fromRegistration) {
			historyPerformedByName = HistoryAuditPeoplePerformedByNames.PLR;
		}
		const currentUser = fromRegistration ? null : await Meteor.userAsync();
		const currentPerson = currentUser ? await currentUser.fetchPerson() : null;
		const currentOrg = orgId ? await Orgs.findOneAsync({ _id: orgId }) : await Orgs.current();

		if (personData.firstName) check(personData.firstName, String);
		if (personData.lastName) check(personData.lastName, String);
		if (personData.middleName) check(personData.middleName, String);
		if (personData.preferredName) check(personData.preferredName, String);
		if (personData.defaultGroupId) check(personData.defaultGroupId + "", String);
		if (personData.type) check(personData.type, String);

		if (!(personData.firstName.trim().length > 0 && personData.lastName.trim().length > 0))
			throw new Meteor.Error(500, "Names cannot be blank");

		if (!personData.overrideNameCheck) {
			const personNameCheck = await People.findOneAsync({
				orgId: orgId || currentUser.orgId,
				firstName: { "$regex": new RegExp(personData.firstName, "i") },
				lastName: { "$regex": new RegExp(personData.lastName, "i") },
				type: personData.type
			});
			if (personNameCheck) throw new Meteor.Error(500, "Name already exists.");
		}

		if (currentPerson && currentPerson.type == "admin" && personData.pinCode) {
			if (!/^[0-9]+$/.test(personData.pinCode)) throw new Meteor.Error(500, "PIN Code must be a number");
			const pinLength = currentOrg.pinLength();
			if (personData.pinCode.length !== pinLength) {
				throw new Meteor.Error(500, `PIN Code must be ${pinLength} digits`);
			}
			let existsPin = false;
			if (currentOrg.pinUniqueAcrossOrgs()) {
				const relatedOrgIds = await currentOrg.findAllRelatedOrgIds();
				const allOrgIds = relatedOrgIds.concat([currentOrg._id]);
				existsPin = await People.findOneAsync({ orgId: { $in: allOrgIds }, pinCode: personData.pinCode });
			} else {
				existsPin = await People.findOneAsync({ orgId: currentOrg._id, pinCode: personData.pinCode });
			}
			if (existsPin || currentOrg.kioskPinCode == personData.pinCode) throw new Meteor.Error(500, "PIN Code already in use for another user");
		}

		if (currentPerson && currentPerson.type == "admin" && personData.pinCodeSupplemental) {
			if (!/^[0-9]+$/.test(personData.pinCodeSupplemental)) throw new Meteor.Error(500, "Supplemental PIN must be a number");
			if (personData.pinCodeSupplemental.length > 15) throw new Meteor.Error(500, "Supplemental PIN must be 15 or less digits");
			const existsPin = await People.findOneAsync({ orgId: currentOrg._id, pinCodeSupplemental: personData.pinCodeSupplemental });
			if (existsPin) throw new Meteor.Error(500, "Supplemental PIN already in use for another user");
		}

		var insertPersonData = {};
		insertPersonData.firstName = personData.firstName;
		insertPersonData.lastName = personData.lastName;
		insertPersonData.middleName = personData.middleName;
		insertPersonData.preferredName = personData.preferredName;
		insertPersonData.designations = personData.designations || [];
		if (personData.defaultGroupId) insertPersonData.defaultGroupId = personData.defaultGroupId;
		if (personData.targetGroupId) insertPersonData.targetGroupId = personData.targetGroupId;
		insertPersonData.type = personData.type;
		if (personData.emailAddress) {
			// Clean and lowercase the email address
			insertPersonData.profileEmailAddress = personData.emailAddress.trim().toLowerCase();
		}

		if (currentOrg.hasCustomization("people/types/customerSpecificStaffTypes") && personData.type == "staff")
			insertPersonData.staffType = personData.staffType;
		if (currentOrg.hasCustomization("counters/people/person") && personData.type == "person" && Meteor.isServer)
			insertPersonData.uid = await currentOrg.nextCounterValue("people");
		const prefix = currentOrg.profileDataPrefix();
		(await People.allowableProfileFieldsForType(personData.type, null, currentOrg)).forEach(function (field) {
			if (personData[field.name]) {
				if (field.type === 'date') {
					try {
						check(personData[field.name], String);
					} catch (e) {
						check(personData[field.name], Number);
					}
				} else {
					check(personData[field.name], String);
				}
				if (prefix) {
					if (!insertPersonData[prefix]) {
						insertPersonData[prefix] = {};
					}
					insertPersonData[prefix][field.name] = personData[field.name];
				} else {
					insertPersonData[field.name] = personData[field.name];
				}
			}
		});

		insertPersonData.createdBy = currentUser ? Meteor.userId() : 'SYSTEM';
		insertPersonData.createdAt = new Date().valueOf();
		insertPersonData.orgId = currentOrg._id;
		insertPersonData.inActive = false;

		// Do not create a person record with an email address that already exists for the within the org;
		if (insertPersonData.profileEmailAddress) {
			const emailExists = await Meteor.callAsync("checkEmailExistsForOrg", insertPersonData.profileEmailAddress, currentOrg._id, regId, true);
			if (emailExists) {
				throw new Meteor.Error(400, "Email already exists for another person in the org");
			}
		}
		const newPersonId = await People.insertAsync(insertPersonData);
		if (Meteor.isServer) {
			// Create history
			const historyOptions = {
				callbackString: HistoryAuditRecordTypes.PERSON,
				changeType: HistoryAuditChangeTypes.ADD,
				performedByUser: currentUser,
				performedByName: historyPerformedByName,
				previousState: null,
				currentState: { ...insertPersonData, _id: newPersonId }
			};
			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for person insert", error);
			});
		}
		if (personData.editNewPersonFamilyId) {
			let inserted = false;
			personData.editNewPersonFamilyType.forEach((type) => {
				const query = {
					personId: newPersonId,
					targetId: personData.editNewPersonFamilyId,
					relationshipType: type,
					primaryCaregiver: (type === "family") ? (personData.primaryCaregiver || false) : false,
					relationshipDescription: personData.editNewPersonFamilyDescription,
					orgId: currentOrg._id
				};

				//"Cheap" fix to allow WMG data to flow from database without ordering issues from the people.insert above
				Meteor.setTimeout(async function () { await Relationships.direct.insertAsync(query); }, 3000);
				inserted = true;
			})
			if (inserted) {
				Meteor.setTimeout(async function () {
					await Meteor.callAsync('sendFamilyToCrm', personData.editNewPersonFamilyId);
					await Meteor.callAsync('setAutoPin', newPersonId);
				}, 5000);
			}
		}

		if (personData.type !== "prospect" && personData.emailAddress && personData.emailAddress.length > 0) {
			check(personData.emailAddress, String);

			const autoCreateStaffAdminAccounts = currentOrg.hasCustomization(AvailableCustomizations.HIDE_SEND_INVITATION_FOR_STAFF_AND_ADMINS);
			const isStaffOrAdmin = [PeopleTypes.ADMIN, PeopleTypes.STAFF].includes(personData.type);
			const autoCreatingAccount = autoCreateStaffAdminAccounts && isStaffOrAdmin;

			if (autoCreatingAccount) {
				// For auto-created accounts, skip invitation and create user directly
				const result = await UserUtils.createAutoAccount({
					personData,
					newPersonId,
					currentOrg
				});

				if (!result.success) {
					console.error("Failed to auto-create account:", result.error);
				}
			} else {
				// Regular flow for non-auto created accounts
				const invitationId = await UserInvitations.insertAsync({
					token: tokenString(),
					email: personData.emailAddress.trim().toLowerCase(),
					orgId: currentOrg._id,
					personId: newPersonId,
					used: false
				});

				const opResult = await insertPersonCreateUser({ personData, newPersonId, orgId });

				if (Meteor.isServer && personData.sendInvite) {
					await processInvitation(invitationId);
				}
			}
		}
		if (personData.type == "person") {
			processAnalytics({
				metaCxData: {
					type: "create-person",
					data: {
						orgId: currentOrg._id,
						orgName: currentOrg.name,
						type: personData.type
					}
				}
			});
		}
		if (personData.type == "family") {

			processAnalytics({
				metaCxData: {
					type: 'create-family',
					data: {
						orgId: currentOrg._id,
						orgName: currentOrg.name,
						type: personData.type
					}
				}
			});
		}
		if (Meteor.isServer && personData.type === 'vendor' && currentOrg.pinUniqueAcrossOrgs()) {
			let success = false;
			let tryCounter = 0;
			while (!success && tryCounter < 10) {
				tryCounter++;
				let pin = Math.floor(Math.random() * 999999); // Math.random returns a number between 0 and 1, multiply to get a number 0 - 999999
				pin = pin.toString().padStart(6, "0"); // pad with leading zeros to make 6 digits
				while ((await People.find({ pinCode: pin }).countAsync()) > 0) {
					// if the pin is already in use, generate a new one
					pin = Math.floor(Math.random() * 999999);
					pin = pin.toString().padStart(6, "0");
				}

				People.updateAsync({ _id: newPersonId }, { $set: { pinCode: pin, pinCodeSupplemental: "" } });
				try {
					await Meteor.callAsync('updateZkTecoPerson', newPersonId, true);
					success = true;
				} catch (e) {
				}
			}
			if (!success) {
				throw new Meteor.Error('PIN error for this vendor.')
			}
		}
		return newPersonId;
	},
	'updatePerson': async function (personData, orgId = null, fromRegistration = false, regId = null, isNew = false) {
		logger.info('>>> Update Person Start ', { personId: personData.personId });
		let historyPerformedByName = null;
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "people/addModifyUsers", action: "edit" }],
				evaluator: (person) => person.type == "admin" || person.type == "staff",
				throwError: true
			});
		} else if (regId || fromRegistration) {
			historyPerformedByName = HistoryAuditPeoplePerformedByNames.PLR;
		}
		const currentUser = orgId ? null : await Meteor.userAsync();
		const p = await People.findOneAsync(personData.personId);
		if (!orgId && !(p["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");
		const org = orgId ? await Orgs.findOneAsync({ _id: orgId }) : await Orgs.current();
		const currentPerson = currentUser && await currentUser.fetchPerson();
		if (personData.firstName) check(personData.firstName, String);
		if (personData.lastName) check(personData.lastName, String);
		if (personData.middleName) check(personData.middleName, String);
		if (personData.preferredName) check(personData.preferredName, String);
		if (personData.defaultGroupId) check(personData.defaultGroupId, String);
		if (personData.targetGroupId) check(personData.targetGroupId, String);
		if (personData.type) check(personData.type, String);

		if (currentPerson && currentPerson.type == "admin" && personData.pinCode) {
			if (!/^[0-9]+$/.test(personData.pinCode)) throw new Meteor.Error(500, "PIN Code must be a number");
			const pinLength = org.pinLength();
			if (personData.pinCode.length !== pinLength) {
				throw new Meteor.Error(500, `PIN Code must be ${pinLength} digits`);
			}
			let existsPin = false;
			if (org.pinUniqueAcrossOrgs()) {
				const allRelatedOrgIds = await org.findAllRelatedOrgIds();
				const allOrgIds = allRelatedOrgIds.concat([org._id]);
				existsPin = await People.findOneAsync({ orgId: { $in: allOrgIds }, pinCode: personData.pinCode, _id: { $ne: p._id } });
			} else {
				existsPin = await People.findOneAsync({ orgId: org._id, pinCode: personData.pinCode, _id: { $ne: p._id } });
			}
			if (existsPin || org.kioskPinCode == personData.pinCode) throw new Meteor.Error(500, "PIN Code already in use for another user");
		}

		if (currentPerson && currentPerson.type == "admin" && personData.pinCodeSupplemental) {
			if (!/^[0-9]+$/.test(personData.pinCodeSupplemental)) throw new Meteor.Error(500, "Supplemental PIN must be a number");
			if (personData.pinCodeSupplemental.length > 15) throw new Meteor.Error(500, "Supplemental PIN must be 15 or less digits");
			const existsPin = await People.findOneAsync({ orgId: org._id, pinCodeSupplemental: personData.pinCodeSupplemental, _id: { $ne: p._id } });
			if (existsPin) throw new Meteor.Error(500, "Supplemental PIN already in use for another user");
		}

		var updatePersonData = {
			firstName: personData.firstName,
			lastName: personData.lastName,
			middleName: personData.middleName,
			preferredName: personData.preferredName,
			defaultGroupId: personData.defaultGroupId,
			targetGroupId: personData.targetGroupId,
			designations: personData.designations || [],
			employeeId: personData.employeeId,
		};

		if (personData?.waitlistAddedDate?.length > 0) {
			updatePersonData.waitlistAddedDate = new moment.tz(personData.waitlistAddedDate, "MM/DD/YYYY", org.getTimezone()).valueOf();
		} else
			updatePersonData.waitlistAddedDate = null;
		if (currentPerson && currentPerson.type == "admin") {
			updatePersonData.type = personData.type;
			if (org.hasCustomization("people/types/customerSpecificStaffTypes") && personData.type == "staff")
				updatePersonData.staffType = personData.staffType;
		}

		if (p["type"] === "staff" && personData.type === "admin") {
			updatePersonData.defaultGroupId = null;
		}
		var personType = updatePersonData.type || p["type"];
		const prefix = org.profileDataPrefix();
		if (prefix) {
			const currentPersonData = await People.findOneAsync({ _id: personData.personId });
			updatePersonData[prefix] = currentPersonData?.[prefix];
		}
		(await People.allowableProfileFieldsForType(personType, null, org)).forEach(function (field) {
			if (personData[field.name]) {
				if (field.type === 'date') {
					try {
						check(personData[field.name], String);
					} catch (e) {
						check(personData[field.name], Number);
					}
				} else {
					check(personData[field.name], String);
				}
				if (prefix) {
					if (!updatePersonData[prefix]) {
						updatePersonData[prefix] = {};
					}
					updatePersonData[prefix][field.name] = personData[field.name];
				} else {
					updatePersonData[field.name] = personData[field.name];
				}
			}
		});

		// allow the editing of person record email addresses for family/staff/admin and store on person profile
		// we will handle the subsequent user update after the Person update
		const personUserRecord = await p.findAssociatedUser();
		const personDataEmailAddress = (personData && personData.emailAddress && !p.superAdmin) ? personData.emailAddress.trim().toLowerCase() : null;
		if (["family", "staff", "admin"].indexOf(p.type) > -1 && !personUserRecord) {
			updatePersonData["profileEmailAddress"] = personDataEmailAddress
		}

		// Do not update a person record with an email address that already exists within the org;
		let needInvite = false;
		const emailAdd = await p.getEmailAddress();
		if (personDataEmailAddress && personDataEmailAddress !== "" && emailAdd !== personDataEmailAddress) {
			needInvite = !!personUserRecord;
			// check if email exists in manage. Don't check cognito since we want to allow linking with enroll, and
			// it's fine since we don't allow user to log in to manage before invitation
			const emailExists = await Meteor.callAsync('checkEmailExistsForOrg', personDataEmailAddress, org._id, regId, true);
			if (emailExists) {
				throw new Meteor.Error(400, "Email already exists for another person in the org");
			}

			const oldEmail = p.getEmailAddress();
			const enrollUser = await Meteor.callAsync('checkIfUserHasEnroll', personUserRecord, org._id);

			if (enrollUser) {
				const recoveryEmailNeedsUpdate = oldEmail && oldEmail === enrollUser.recovery_email;
				if (recoveryEmailNeedsUpdate) {
					await Meteor.callAsync('updateEnrollRecoveryEmail', enrollUser, personDataEmailAddress, org._id);
				}
			}

			if (oldEmail) {
				//Remove old email from user pool
				await Meteor.callAsync('removeUserFromPool', oldEmail, p.orgId, p._id);
				// update profile address since we might be deleting the user
				updatePersonData["profileEmailAddress"] = personDataEmailAddress
			}
		}

		await People.updateAsync(personData.personId, {
			$set: updatePersonData
		});

		if (Meteor.isServer && isNew) {
			// Create history
			const historyOptions = {
				callbackString: HistoryAuditRecordTypes.PERSON,
				changeType: HistoryAuditChangeTypes.ADD,
				performedByUser: currentUser,
				performedByName: historyPerformedByName,
				previousState: null,
				currentState: { ...updatePersonData, _id: personData.personId, orgId: p.orgId ?? org._id }
			};
			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for person insert", error);
			});
		}

		if (personDataEmailAddress && personDataEmailAddress !== "") {
			if (personUserRecord) {
				await Meteor.users.updateAsync({ _id: personUserRecord._id }, { $set: { "emails": [{ address: personDataEmailAddress }] } });
				if (needInvite) {
					await Meteor.users.updateAsync({ _id: personUserRecord._id }, { $set: { pending: true } });
				}
				// This is to update first name, last name, and email
				if (p.type === 'admin') {
					await Meteor.callAsync('sendManageUserUpdates', personUserRecord);
				}
				// always send invite if user changed email
			}
		}
		else {
			if (p.type === 'admin') {
				await Meteor.callAsync('sendManageUserUpdates', personUserRecord);
			}
		}
		if (needInvite) {
			const invitationId = await UserInvitations.insertAsync({
				token: tokenString(),
				email: personDataEmailAddress,
				orgId: org._id,
				personId: p._id,
				used: false
			});
			if (Meteor.isServer) await processInvitation(invitationId);
		}
		if (Meteor.isServer && personData.primaryOrgId || personData.membershipOrgIds) {
			if (!personData.workableOrgsExist) {
				await Meteor.callAsync('setPrimarySiteForPerson', personData.personId);
			} else {
				await Meteor.callAsync('updateWorkableOrgsForStaff', personData.personId, personData.membershipOrgIds, personData.primaryOrgId);
			}
		}
		if (Meteor.isServer) {
			await Meteor.callAsync('updateZkTecoPerson', personData.personId);
		}
		logger.info('<<< Update Person Finished', { personId: personData.personId });
	},
	'deletePerson': async function (personId) {
		await processPermissions({
			assertions: [{ context: "people/deactivateUsers", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		var personTarget = await People.findOneAsync(personId);
		if (!(personTarget.orgId === (await Meteor.userAsync())["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const personAssociatedUser = await personTarget.findAssociatedUser();
		if (personAssociatedUser && Meteor.isServer) {
			if (personTarget.type === 'admin') {
				const personAssociatedUser = await personTarget.findAssociatedUser();
				await Meteor.callAsync('sendManageUserUpdates', personAssociatedUser, true);
			}
			await deletePersonUpdateOrDeleteUser({ person: personTarget });
		}

		await Relationships.removeAsync({ $or: [{ "personId": personId }, { "targetId": personId }] });
		await People.removeAsync(personId);
		if (Meteor.isServer) {
			await Meteor.callAsync('deleteZkTecoPerson', personId);
		}
	},
	'updateDefaultGroupId': async function (payload) {
		await processPermissions({
			assertions: [{ context: "people/addModifyUsers", action: "edit" }],
			evaluator: (person) => person.type === "admin" || person.type === "staff",
			throwError: true
		});
		if (payload.defaultGroupId !== "default") {
			await People.updateAsync({ _id: payload.personId }, { $set: { defaultGroupId: payload.defaultGroupId } });
		}
	},
	'insertUser': async function (userData) {
		await processPermissions({
			assertions: [{ context: "people/addModifyUsers", action: "edit" }],
			evaluator: (person) => person.type === "admin" || person.type === "staff",
			throwError: true
		});

		check(userData.email, String);
		check(userData.firstName, String);
		check(userData.lastName, String);

		await Accounts.createUserAsync({
			email: userData.email.trim().toLowerCase(),
			userData: {
				firstName: userData.firstName,
				lastName: userData.lastName
			},
			orgId: (await Meteor.userAsync())["orgId"]
		});
	},
	'checkInMultiple': async function (options) {
		if (Meteor.isServer) {
			var currentUser = await Meteor.userAsync();
			const userPerson = await currentUser?.fetchPerson();
			if (!currentUser ||
				!(userPerson.type == "admin" || userPerson.type == "staff")) {
				throw new Meteor.Error(403, "Access denied");
			}
			return new Promise(async (resolve, reject) => {
				const persons = await People.find({ orgId: currentUser.orgId, _id: { $in: options.selectedPeople } }).fetchAsync();
				let fn = (person) => {
					return new Promise(async (resolve, reject) => {
						if (person && !person.checkedIn) {
							await Meteor.callAsync("checkIn", { personId: person._id, checkInDefaultGroup: true })
								.then((res) => {
									setTimeout(() => {
										resolve({ "personId": person._id, "checkedIn": true });
									}, 500);
								})
								.catch((error) => {
									setTimeout(() => {
										reject(error);
									}, 500);
								});
						}
						else {
							setTimeout(() => {
								resolve({ "personId": person._id });
							}, 500);
						}
					});
				};
				let actions = persons.map(fn); // run the function over all persons
				await Promise.all(actions)
					.then((results) => {
						let filteredResults = results.filter((element) => {
							return element.checkedIn === true;
						});
						resolve(filteredResults);
					})
					.catch((error) => {
						reject(error);
					});
			})
				.then((results) => {
					return { checkInCount: results.length };
				})
				.catch((error) => {
					throw new Meteor.Error(error.error, error.reason);
				});
		}
	},
	'checkOutMultiple': async function (options) {
		if (Meteor.isServer) {
			var currentUser = await Meteor.userAsync();
			const userPerson = await currentUser?.fetchPerson();
			const org = await Orgs.current();
			const timezone = org.getTimezone();
			if (!currentUser ||
				!(userPerson.type == "admin" || userPerson.type == "staff")) {
				throw new Meteor.Error(403, "Access denied");
			}
			return new Promise(async (resolve, reject) => {
				const persons = await People.find({ orgId: currentUser.orgId, _id: { $in: options.selectedPeople } }).fetchAsync();
				let fn = (person) => {
					return new Promise(async (resolve, reject) => {
						if (person && person.checkedIn) {
							await Meteor.callAsync("checkOut", { personId: person._id, multipleCheckoutEmail: true })
								.then((res) => {
									setTimeout(() => {
										resolve({ "personId": person._id, "checkedOut": true });
									}, 500);
								})
								.catch((error) => {
									setTimeout(() => {
										reject(error);
									}, 500);
								});
						}
						else {
							setTimeout(() => {
								resolve({ "personId": person._id });
							}, 500);

						}
					});
				};
				let actions = persons.map(fn); // run the function over all persons
				await Promise.all(actions)
					.then((results) => {
						let filteredResults = results.filter((element) => {
							return element.checkedOut === true;
						});
						resolve(filteredResults);
					})
					.catch((error) => {
						reject(error);
					});
			}).then((results) => {
				Meteor.defer(async function () {
					if (new Date().valueOf() > new moment().tz(timezone).startOf('day') && results.length > 0) {
						for (let person of results) {
							if (person.checkedOut) {
								await processSummaryMail2021(person.personId, "");
							}
						}
					}
				})
				return { checkInCount: results.length };
			})
				.catch((error) => {
					throw new Meteor.Error(error.error, error.reason);
				});
		}
	},
	checkIn,
	'updateCheckIn': async function (checkInData) {
		var currentUser = await Meteor.userAsync();
		const userPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			!(userPerson.type == "admin" || userPerson.type == "staff") ||
			!((await People.findOneAsync(checkInData.personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		var targetPerson = await People.findOneAsync(checkInData.personId);

		if (userPerson.type == "staff" && (await Orgs.current()).hasCustomization("moments/checkin/staffLockdown")
			&& (targetPerson.type == "staff" || targetPerson.type == "admin") && currentUser.personId != checkInData.personId)
			throw new Meteor.Error(403, "Access denied -- staff can only checkout themselves");

		if (userPerson.type == "staff" && targetPerson.type != "person"
			&& (await Orgs.current()).hasCustomization("people/staffRequiredPinCodeCheckin/enabled"))
			throw new Meteor.Error(403, "Access denied -- staff are unable to modify check in");

		await People.updateAsync(checkInData.personId, { $set: { checkedInOutTime: checkInData.time } });
	},
	checkOut,
	'insertManualTimeEntry': async function (options) {
		/*
			NOTE: There seems to be a race condition on the SQS message parsing for Timce Cards when entering the Checkin and Checkout
			moments back-to-back.  This function will force create the TimeCard entry first with relevant data so the checkout moment can attach to it
		*/
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			!(currentPerson?.type == "admin"))
			throw new Meteor.Error(403, "Access denied");

		let newMoment = {};

		const startTime = new moment.tz(options.date + " " + options.timePickerStart, "MM/DD/YYYY HH:mm", (await Orgs.current()).getTimezone()),
			endTime = new moment.tz(options.date + " " + options.timePickerEnd, "MM/DD/YYYY HH:mm", (await Orgs.current()).getTimezone()),
			date = new moment.tz(options.date, "MM/DD/YYYY", (await Orgs.current()).getTimezone());

		if (endTime.valueOf() < startTime.valueOf)
			throw new Meteor.Error(500, "Start time must be greater than end time.");
		const checkInPerson = await People.findOneAsync({ _id: options.personId, type: { "$in": ["staff", "admin"] }, orgId: currentUser.orgId });
		if (!checkInPerson)
			throw new Meteor.Error(500, "No such person exists in your organization");


		var checkinComment = (currentPerson) ? `Checked in by ${currentPerson.firstName} ${currentPerson.lastName}` : "Manual time entry check-in";
		var checkoutComment = (currentPerson) ? `Checked out by ${currentPerson.firstName} ${currentPerson.lastName}` : "Manual time entry check-out";
		newMoment["createdAt"] = new Date().valueOf();
		newMoment["createdBy"] = currentUser._id;
		newMoment["createdByPersonId"] = currentUser.personId;
		newMoment.orgId = currentUser.orgId;
		newMoment["owner"] = options.personId;
		newMoment["taggedPeople"] = [options.personId];
		newMoment["momentType"] = "checkin";
		newMoment["momentTypePretty"] = "Check In";
		newMoment["time"] = startTime.format("h:mm a");
		newMoment["sortStamp"] = startTime.valueOf();
		newMoment["date"] = date.format("MM/DD/YYYY");
		newMoment["comment"] = checkinComment;
		newMoment["manualTimeEntryClassification"] = options.classification;
		newMoment["manualTimeCardEntryCreated"] = true;
		newMoment["selectedPayTypeId"] = options.classification;

		let newEndMoment = _.clone(newMoment);
		const checkInMomentId = await Moments.insertAsync(newMoment);

		const timeCardDoc = {
			personId: options.personId,
			orgId: currentUser.orgId,
			checkInMomentId: checkInMomentId,
			originalCheckInMoment: newMoment,
			checkInTime: startTime.format("h:mm a"),
			checkInDate: date.format("MM/DD/YYYY"),
			checkOutDate: date.format("MM/DD/YYYY"),
			checkOutTime: endTime.format("h:mm a"),
			timeCardDateStamp: date.startOf("day").valueOf(),
			selectedPayTypeId: options.classification,
		};

		await TimeCards.insertAsync(timeCardDoc);

		newEndMoment["momentType"] = "checkout";
		newEndMoment["momentTypePretty"] = "Check Out";
		newEndMoment["time"] = endTime.format("h:mm a");
		newEndMoment["sortStamp"] = endTime.valueOf();
		newEndMoment["comment"] = checkoutComment;
		newEndMoment["checkInMomentId"] = checkInMomentId;

		await Moments.insertAsync(newEndMoment);

	},
	'resendSummaryEmail': async function (personId, sendACopy) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			!(currentPerson?.type == "admin" || currentPerson?.type == "staff") ||
			!((await People.findOneAsync(personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");
		var summaryCC = (sendACopy) ? currentUser.emails[0].address : "";
		if (Meteor.isServer) {
			Meteor.defer(async function () {
				await processSummaryMail2021(personId, summaryCC);
			})
		}
	},
	'switchGroup': async function (switchData) {
		await processPermissions({
			assertions: [{ context: "people/movement", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();

		const currentPerson = await People.findOneAsync({ _id: switchData.personId, orgId: currentUser["orgId"] });

		var peopleToSwitch;
		if (switchData.switchAllInGroup) {
			peopleToSwitch = (await People.find({ orgId: currentUser["orgId"], checkInGroupId: currentPerson.checkInGroupId, checkedIn: true }).fetchAsync()).map(function (person) { return person._id; });
		} else if (switchData.peopleIds) {
			peopleToSwitch = (await People.find({ orgId: currentUser["orgId"], checkedIn: true, _id: { $in: switchData.peopleIds } }).fetchAsync()).map(function (person) { return person._id; });
		}
		else {
			peopleToSwitch = [switchData.personId];
		}
		let destinationGroup;
		const groupsToRecalculate = [];
		if (switchData.groupId) {
			destinationGroup = await Groups.findOneAsync(switchData.groupId);
			if (!destinationGroup) {
				throw new Meteor.Error('500', 'Group not found');
			}
			groupsToRecalculate.push(switchData.groupId);
		}

		let currentPersonSwitched = false;
		if (peopleToSwitch.indexOf(switchData.personId)) {
			currentPersonSwitched = true;
		}

		for (const personId of peopleToSwitch) {
			var p = await People.findOneAsync({ orgId: currentUser.orgId, _id: personId });
			const getGroup = await Groups.findOneAsync({ _id: p.checkInGroupId });
			const currentGroup = (p.checkInGroupId) ? getGroup : null;
			var oldCheckInGroupName = p.checkInGroupName || "none",
				newCheckInGroupName = (destinationGroup && destinationGroup.name) || "none",
				previousCheckInGroupId = p.checkInGroupId,
				previousCheckInGroupName = p.checkInGroupName,
				destinationGroupId = destinationGroup && destinationGroup._id,
				destinationGroupName = destinationGroup && destinationGroup.name;

			if (previousCheckInGroupId) groupsToRecalculate.push(previousCheckInGroupId)
			if (p?.mediaRequirements?.mediaReviewRequired == "Yes" || p?.profileData?.mediaRequirements?.mediaReviewRequired == "Yes") {
				await Groups.updateAsync({ _id: switchData.groupId }, { $set: { mediaReviewRequired: true } });
			}

			var personUpdate = {
				checkInGroupId: switchData.groupId,
				checkInGroupName: destinationGroup && newCheckInGroupName,
				presenceLastGroupId: currentGroup?._id,
			};

			if (destinationGroup && destinationGroup.includeClassList) {
				personUpdate.previousClassroomGroupId = destinationGroup._id;
			} else if (currentGroup && currentGroup.includeClassList) {
				personUpdate.previousClassroomGroupId = currentGroup._id;
			}

			await People.updateAsync(personId, {
				$set: personUpdate
			});

			const org = await Orgs.current();
			const timezone = org.getTimezone();
			const momentDate = new moment.tz(timezone)

			const newMoment = {};
			newMoment["createdAt"] = new Date().valueOf();
			newMoment["sortStamp"] = newMoment["createdAt"];
			newMoment["createdBy"] = currentUser._id;
			newMoment["createdByPersonId"] = currentUser.personId;
			newMoment.orgId = (await Meteor.userAsync())["orgId"];
			newMoment["comment"] = "Moved from " + oldCheckInGroupName + " to " + newCheckInGroupName;
			newMoment["owner"] = p._id;
			newMoment["taggedPeople"] = [p._id];
			newMoment["momentType"] = "move";
			newMoment["momentTypePretty"] = "Moved";
			newMoment["checkInGroupId"] = destinationGroupId;
			newMoment["checkInGroupName"] = destinationGroupName;
			newMoment["checkOutGroupId"] = previousCheckInGroupId;
			newMoment["checkOutGroupName"] = previousCheckInGroupName;
			newMoment["time"] = momentDate.format("h:mm a");
			newMoment["date"] = momentDate.format("MM/DD/YYYY");
			await Moments.insertAsync(newMoment);
		}

		//Yuck - a hack to force the mobile app to pull for data when bulk moving kids
		Meteor.setTimeout(async function () {
			await People.updateAsync({ _id: currentUser.personId }, { $set: { forceUpdateField: new Date().valueOf() } })
		}, 1500);

		// this will hopefully resolve that annoying -- web: Exception in defer callback: Error: Meteor.userId can only be invoked in method calls or publications.
		// Because without a passed in org, recalculateGroupMediaDesignation() calls org.current() which calls Meteor.userId() which is not allowed.
		const currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		Meteor.defer(async function () {
			const groups = _.uniq(groupsToRecalculate);
			if (groups.length > 0) {
				for (const groupId of groups) {
					await recalculateGroupMediaDesignation(groupId, currentOrg);
				}
			}
		})

		return { currentPersonSwitched };
	},
	'nameToFacePeopleFilter': async function ({ filterGroupId }) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		const org = await Orgs.current();
		const nameToFaceCheck = org.hasCustomization("moments/incompleteFTF/enabled");
		const currentPerson = (currentUser) ? await currentUser.fetchPerson() : { type: "unknown" };
		if (!currentUser || !(currentPerson.type == "admin" || currentPerson.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}

		if (!currentPerson.checkedIn) {
			throw new Meteor.Error(500, "You must be checked-in to complete a name-to-face");
		}

		const curTimestamp = Date.now(), cacheKey = `nameToFacePeopleFilter|${currentUser._id}|${filterGroupId}`;
		if (myMethodCache[cacheKey] && myMethodCache[cacheKey]["timestamp"] > (curTimestamp - 10000)) {
			return myMethodCache[cacheKey]["data"];
		}

		const currentGroup = await Groups.findOneAsync({ _id: filterGroupId });

		const availablePeopleQuery = {
			orgId: currentPerson.orgId,
			checkedIn: true,
			inActive: { $ne: true },
			type: { "$in": ["person", "staff", "admin"] },
		}
		if (nameToFaceCheck) {
			availablePeopleQuery.previousClassroomGroupId = currentPerson.previousClassroomGroupId
		}
		let returnData = { peopleIds: [] };
		if (currentPerson.checkInGroupId == filterGroupId) {
			availablePeopleQuery.checkInGroupId = filterGroupId
			const allPeople = (await People.find(availablePeopleQuery, { fields: { _id: 1 } }).fetchAsync()).map((p) => { return p._id });
			returnData = { peopleIds: allPeople };
		}
		myMethodCache[cacheKey] = {
			timestamp: curTimestamp,
			data: returnData
		}
		return returnData;
	},
	'nameToFaceCheckConfirmation': async function ({ selectedPeople, completedById, filterGroupId, inComplete, moment_type }) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = (currentUser) ? await currentUser.fetchPerson() : { type: "unknown" };
		if (!currentUser || !(currentPerson.type == "admin" || currentPerson.type == "staff")) {
			throw new Meteor.Error(403, "Access denied");
		}

		const org = await Orgs.current();
		const timezone = org.getTimezone();
		const requiresCompletedBy = org.hasCustomization("people/nametoface/requiresCompletedBy");
		const nameToFaceCheck = org.hasCustomization("moments/incompleteFTF/enabled");
		const group = await Groups.findOneAsync({ orgId: org._id, _id: filterGroupId });
		const previousClassroomGroupId = await Groups.findOneAsync({ _id: currentPerson.previousClassroomGroupId, orgId: org._id });
		const previousClassroomGroup = currentPerson.previousClassroomGroupId ? previousClassroomGroupId : null;

		if (!group) {
			throw new Meteor.Error(500, "You must filter by an active group to use name-to-face.");
		}

		// if the group is designated as a classroom then we want to use filterGroupId to confirm, otherwise the previousClassroomGroup
		let peopleGroupIdFilter = filterGroupId;
		if (previousClassroomGroup) {
			peopleGroupIdFilter = (group.includeClassList) ? group._id : previousClassroomGroup._id
		}

		if (requiresCompletedBy && !completedById) {
			throw new Meteor.Error(500, "You must supply a completed by staff/admin to post this name-to-face.");
		}

		let momentDescription = "", actionCount = 0;

		for (const personId of selectedPeople) {
			const person = await People.findOneAsync({ orgId: org._id, _id: personId });
			if (!person) return;
			if (!_.contains(["person", "staff", "admin"], person.type)) return;

			if (!person.checkedIn) {
				throw new Meteor.Error(500, `${person.firstName} ${person.lastName} is not checked in and cannot be included in name-to-face`);
			}

			//if the group is designated as a classroom (classList) or the previousClassroomGroup is null then process on current group
			if (peopleGroupIdFilter == group._id && person.checkInGroupId != group._id) {
				throw new Meteor.Error(500, `${person.firstName} ${person.lastName} is not active in the current group filter ${group.name}`);
			} else if (nameToFaceCheck && previousClassroomGroup && peopleGroupIdFilter == previousClassroomGroup._id && person.previousClassroomGroupId != previousClassroomGroup._id) {
				throw new Meteor.Error(500, `${person.firstName} ${person.lastName} is not active in designated group ${previousClassroomGroup.name}`);
			}

			momentDescription += `Confirmed ${person.firstName} ${person.lastName}\n`;
			actionCount++;
		};

		const availablePeopleQuery = {
			inActive: { $ne: true },
			checkedIn: true,
			orgId: org._id,
			type: "person",
		};


		availablePeopleQuery.checkInGroupId = filterGroupId;
		if (nameToFaceCheck) {
			availablePeopleQuery.previousClassroomGroupId = currentPerson.previousClassroomGroupId
		}
		if (!inComplete) {
			const allAvailablePeopleForGroup = await People.find(availablePeopleQuery).fetchAsync();
			if (allAvailablePeopleForGroup) {
				let personList = "";
				_.each(allAvailablePeopleForGroup, (person) => {
					if (!_.contains(selectedPeople, person._id)) {
						personList += `${person.firstName} ${person.lastName} | `;
					}
				});

				if (personList.length > 0) {
					throw new Meteor.Error(500, `Missing the following checked in person(s) for a successful name-to-face check: ${personList}`)
				}
			}
		}


		if (actionCount > 0) {
			let newMoment = {};
			const momentDate = new moment.tz(timezone);
			newMoment["createdAt"] = momentDate.valueOf();
			newMoment["sortStamp"] = newMoment["createdAt"];
			newMoment["createdBy"] = currentUser._id;
			newMoment["createdByPersonId"] = currentUser.personId;
			newMoment.orgId = org._id;
			newMoment["comment"] = `${actionCount}/${actionCount} confirmed.\n`;
			newMoment["nameToFaceDescription"] = momentDescription;
			newMoment["nameToFaceGroupId"] = filterGroupId;
			newMoment["nameToFaceClassroomGroupId"] = peopleGroupIdFilter;
			newMoment["owner"] = currentPerson._id;
			newMoment["taggedPeople"] = selectedPeople || [];
			if (inComplete !== undefined && moment_type) {
				newMoment["momentType"] = inComplete ? "nameToFace" : moment_type.replace(/\s/g, '').toLowerCase();
				newMoment["momentTypePretty"] = inComplete ? "Name To Face" : moment_type;
			} else {
				newMoment["momentType"] = "nameToFace";
				newMoment["momentTypePretty"] = "Name To Face";
			}
			newMoment["time"] = momentDate.format("h:mm a");
			newMoment["date"] = momentDate.format("MM/DD/YYYY");
			if (requiresCompletedBy) {
				const completedByPerson = await People.findOneAsync({ orgId: org._id, _id: completedById });
				if (completedByPerson) {
					newMoment["nameToFaceCompletedById"] = completedById;
					newMoment["nameToFaceCompletedByPersonName"] = `${completedByPerson.firstName} ${completedByPerson.lastName}`;
				}
			}
			await Moments.insertAsync(newMoment);
		} else {
			throw new Meteor.Error(500, `No action saved for name-to-face request`);
		}
		if (Meteor.isServer) {
			processAnalytics({
				metaCxData: {
					type: 'name-to-face-check', data: {
						orgId: (await Meteor.userAsync())["orgId"],
						orgName: (await Orgs.current()).name,
						personId: currentPerson._id,
						type: currentPerson.type,
						groupId: filterGroupId
					}
				}
			});
			//this should run async
			recalculateGroupDashboard(filterGroupId);
		}
	},
	'nameToFaceCheck': async function (options) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();

		if (!currentUser ||
			!(currentPerson?.type == "admin" || currentPerson?.type == "staff"))
			throw new Meteor.Error(403, "Access denied");

		const timezone = (await Orgs.current()).getTimezone();

		const requiresCompletedBy = (await Orgs.current()).hasCustomization("people/nametoface/requiresCompletedBy");
		if (requiresCompletedBy && !options.completedById)
			throw new Meteor.Error(500, "You must supply a completed by staff/admin to post this name-to-face.");

		let momentDescription = "", summaryDescription = "", actionCount = 0, confirmedCount = 0, movedCount = 0, checkoutCount = 0;
		for (const action of options.actions) {

			const person = await People.findOneAsync({ orgId: currentUser.orgId, _id: action.personId });

			if (!person.checkedIn)
				throw new Meteor.Error(500, person.firstName + " " + person.lastName + " is not checked in and cannot be included in name-to-face");
			if (action.action == "checkout") {
				await Meteor.callAsync("checkOut", { personId: action.personId });
				momentDescription += "Checked out " + person.firstName + " " + person.lastName + "\n";
				checkoutCount++;
			} else if (action.action == "move") {
				await Meteor.callAsync("switchGroup", { personId: action.personId, groupId: action.moveDestination });
				const group = await Groups.findOneAsync({ orgId: currentUser.orgId, _id: action.moveDestination });
				momentDescription += "Moved " + person.firstName + " " + person.lastName + "to " + group.name + "\n";
				movedCount++;
			} else {
				momentDescription += "Confirmed " + person.firstName + " " + person.lastName + "\n";
				confirmedCount++;
			}
			actionCount++;
		};

		if (checkoutCount > 0) summaryDescription += checkoutCount + " checked out.\n";
		if (movedCount > 0) summaryDescription += movedCount + " moved.\n";
		summaryDescription += confirmedCount + "/" + actionCount + " confirmed.\n";

		if (actionCount > 0) {
			let newMoment = {};
			const momentDate = new moment.tz(timezone);
			newMoment["createdAt"] = momentDate.valueOf();
			newMoment["sortStamp"] = newMoment["createdAt"];
			newMoment["createdBy"] = currentUser._id;
			newMoment["createdByPersonId"] = currentUser.personId;
			newMoment.orgId = (await Meteor.userAsync())["orgId"];
			newMoment["comment"] = summaryDescription;
			newMoment["nameToFaceDescription"] = momentDescription;
			newMoment["nameToFaceGroupId"] = options.checkInGroupId;
			newMoment["owner"] = currentPerson._id;
			newMoment["taggedPeople"] = [currentPerson._id];
			newMoment["momentType"] = "nameToFace";
			newMoment["momentTypePretty"] = "Name To Face";
			newMoment["time"] = momentDate.format("h:mm a");
			newMoment["date"] = momentDate.format("MM/DD/YYYY");
			if (requiresCompletedBy) {
				const completedByPerson = await People.findOneAsync({ orgId: (await Meteor.userAsync())["orgId"], _id: options.completedById });
				if (completedByPerson) {
					newMoment["nameToFaceCompletedById"] = options.completedById;
					newMoment["nameToFaceCompletedByPersonName"] = completedByPerson.firstName + ' ' + completedByPerson.lastName;
				}
			}
			await Moments.insertAsync(newMoment);
		}
		if (Meteor.isServer) {
			processAnalytics({
				metaCxData: {
					type: 'name-to-face-check', data: {
						orgId: (await Meteor.userAsync())["orgId"],
						orgName: (await Orgs.current()).name,
						personId: currentPerson._id,
						type: currentPerson.type,
						groupId: options.checkInGroupId
					}
				}
			});
		}
	},
	'addRelationship': async function (relationshipData, orgId = null, fromRegistration = false, fromPlr = false) {
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "people/relationships", action: "edit" }],
				evaluator: (thisPerson) => thisPerson.type == "admin",
				throwError: true
			});
		}
		const currentUser = fromRegistration ? null : await Meteor.userAsync();
		const currentOrgId = orgId || (await Meteor.userAsync())["orgId"];
		const p = await People.findOneAsync(relationshipData.personId);
		if (!fromRegistration && (!(p["orgId"] === currentUser["orgId"]) ||
			!(_.contains["family", "authorizedPickup"], relationshipData.relationshipType))) {
			throw new Meteor.Error(403, "Access denied");
		}

		await createAllRelationships(
			relationshipData.personId,
			relationshipData.targetPersonId,
			currentOrgId,
			relationshipData.primaryCaregiver,
			relationshipData.relationshipType,
			relationshipData.relationshipDescription,
			fromPlr
		);

		if (relationshipData.primaryCaregiver === true && Meteor.isServer) {
			const person = await People.findOneAsync({ _id: relationshipData.targetPersonId });
			if (person) {
				const integrationInfo = await getPersonHubspotAndAirslateIds(person);
				if (integrationInfo) {
					await People.updateAsync(
						{ _id: relationshipData.targetPersonId },
						{
							$set: Object.assign(integrationInfo, { importSlateDocumentId: true })
						}
					);
				}
			}
		}
	},
	'associateAvatarImage': async function (personId, avatarToken) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		var p = await People.findOneAsync(personId);
		if (!currentUser ||
			!(currentPerson?.type == "admin" ||
				currentPerson?.type == "staff" ||
				currentPerson?._id == personId)
			||
			!(p["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		await People.updateAsync(personId, {
			$set: { avatarToken: avatarToken, avatarPath: currentUser.orgId + "/" + currentUser._id + "/" + avatarToken }
		});
	},

	'deleteMoment': async function (momentId) {
		await processPermissions({
			assertions: [{ context: "moments", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		var curMoment = await Moments.findOneAsync(momentId);
		if (!currentUser ||
			!(curMoment["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const curPerson = await People.findOneAsync(curMoment.owner);

		if ((curMoment["momentType"] == "checkin" || curMoment["momentType"] == "checkout")
			&& (curPerson.type == "staff" || curPerson.type == "admin"))
			throw new Meteor.Error(403, "Access denied -- cannot delete checkin/checkout for staff/admin");

		await Moments.removeAsync(momentId);

		processAnalytics({
			metaCxData: {
				type: 'deleted-moment', data: {
					orgId: currentUser["orgId"],
				}
			}
		});
	},
	'updateSubscription': async function (subscriptionData) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		const rel = await Relationships.findOneAsync(subscriptionData.id);
		if (!currentUser ||
			!((currentPerson?.type == "admin") ||
				((currentPerson?.type == "family") &&
					(rel["personId"] != currentUser._id))
			))
			throw new Meteor.Error(403, "Access denied");

		var dict = {};
		if (subscriptionData.momentType == "dailySummary") {
			dict["suspendDailySummaryEmails"] = !subscriptionData.status;
		} else if (subscriptionData.momentType === 'familyMessagesOptOut') {
			dict['familyMessagesOptOut'] = subscriptionData.status;
		} else {
			dict["subscriptions." + subscriptionData.momentType + "." + subscriptionData.method] = subscriptionData.status;
		}
		await Relationships.updateAsync(subscriptionData.id, { $set: dict });
	},
	'deleteAnnouncementReminder': async function ({ announcementId, reminderId }) {
		await processPermissions({
			assertions: [{ context: "announcements", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		await cancelAnnouncementReminder(announcementId, reminderId);
	},
	'insertAnnouncement': async function (announcementData) {
		try {
			await processPermissions({
				assertions: [{ context: "announcements", action: "edit" }],
				evaluator: (person) => person.type == "admin" || person.type == "staff",
				throwError: true
			});
			const { currentUser, currentPerson, currentOrg } = await InsertUpdateAnnouncementsService.getUserPersonOrg();
			const mediaFiles = InsertUpdateAnnouncementsService.processMediaFiles(announcementData?.newMediaFiles);
			const reminders = InsertUpdateAnnouncementsService.processReminders(announcementData?.reminders);
			const announcement = InsertUpdateAnnouncementsService.createAnnouncementObject(announcementData, currentUser, mediaFiles, reminders, currentOrg);
			let { announcementIds, staleReminders } = await InsertUpdateAnnouncementsService.InsertAnnouncement(announcement, announcementData, currentPerson, currentOrg);
			staleReminders = InsertUpdateAnnouncementsService.checkStaleReminders(reminders, staleReminders, currentOrg);
			InsertUpdateAnnouncementsService.scheduleReminders(announcementIds, reminders)
			if (staleReminders.length) {
				return staleReminders
			}
		} catch (error) {
			Log.error("Error in 'insertAnnouncement':" + error);
			throw new Meteor.Error(error.error || error.message);
		}
	},
	'updateAnnouncement': async function (announcementData) {
		try {
			await processPermissions({
				assertions: [{ context: "announcements", action: "edit" }],
				evaluator: (person) => person.type == "admin" || person.type == "staff",
				throwError: true
			});
			const { currentUser, currentPerson, currentOrg } = await InsertUpdateAnnouncementsService.getUserPersonOrg();
			const announcement = await Announcements.findOneAsync(announcementData.announcementId);
			if (!(currentUser.orgId == announcement.orgId)) {
				throw new Meteor.Error(403, "Access denied");
			}
			let mediaFiles = announcement?.mediaFiles || [];
			let reminders = announcement?.reminders || [];
			let newMediaFiles = InsertUpdateAnnouncementsService.processMediaFiles(announcementData?.newMediaFiles);
			mediaFiles = (newMediaFiles && newMediaFiles.length > 0) ? ((mediaFiles && mediaFiles.length > 0) ? mediaFiles.concat(newMediaFiles) : newMediaFiles) : mediaFiles;
			let newReminders = InsertUpdateAnnouncementsService.processReminders(announcementData?.reminders);
			reminders = (newReminders && newReminders.length > 0) ? ((reminders && reminders.length > 0) ? reminders.concat(newReminders) : newReminders) : reminders;
			const updateSet = InsertUpdateAnnouncementsService.getUpdateSet(announcementData, currentOrg, mediaFiles, reminders);
			const { announcementIds, staleReminders } = await InsertUpdateAnnouncementsService.updateAnnouncement(updateSet, announcement, announcementData, currentPerson, currentUser);
			InsertUpdateAnnouncementsService.scheduleReminders(announcementIds, reminders)
			if (staleReminders.length) {
				return staleReminders;
			}
		} catch (error) {
			Log.error("Error in 'updateAnnouncement':" + error);
			throw new Meteor.Error(error.error || error.message);
		}
	},
	'deleteAnnouncement': async function (announcementId) {
		await processPermissions({
			assertions: [{ context: "announcements", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		const announcement = await Announcements.findOneAsync(announcementId);
		if (!(currentUser.orgId == announcement.orgId))
			throw new Meteor.Error(403, "Access denied");

		await Announcements.removeAsync(announcementId);
	},
	'deleteMasterAnnouncement': async function (announcementId, deleteAll) {
		await processPermissions({
			assertions: [{ context: "announcements", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		var currentUser = await Meteor.userAsync();
		var currentAnnouncement = await Announcements.findOneAsync(announcementId);
		if (!(currentUser.orgId == currentAnnouncement.orgId))
			throw new Meteor.Error(403, "Access denied");

		var currentPerson = await currentUser?.fetchPerson();
		if (currentPerson && !currentPerson.isMasterAdmin())
			throw new Meteor.Error(403, "Access denied - person permissions");

		if (currentAnnouncement.masterAdminEditId && deleteAll) {
			await Announcements.removeAsync({ masterAdminEditId: currentAnnouncement.masterAdminEditId });
		} else {
			await Announcements.removeAsync(announcementId);
			if (currentAnnouncement.masterAdminEditId) {
				var remainingOrgs = (await Announcements.find({ masterAdminEditId: currentAnnouncement.masterAdminEditId }, { fields: { orgId: 1 } }).fetchAsync()).map((a) => { return a.orgId });
				await Announcements.updateAsync({ masterAdminEditId: currentAnnouncement.masterAdminEditId }, { $set: { selectedOrgs: remainingOrgs } })
			}
		}
	},
	'insertCurriculum': async function (curriculumData) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();

		if (curriculumData.headline.trim == "")
			throw new Meteor.Error(500, "The title for an activity cannot be blank.");

		var mediaFiles = [];

		if (curriculumData.newMediaFile) {
			let newFiles;
			if (Array.isArray(curriculumData.newMediaFile))
				newFiles = curriculumData.newMediaFile;
			else
				newFiles = [curriculumData.newMediaFile];
			_.each(newFiles, (nf) => {
				const newFile = {
					mediaName: nf.name,
					mediaUrl: nf.mediaUrl,
					mediaToken: nf.mediaToken,
					mediaFileType: nf.mediaFileType,
					mediaPath: nf.mediaPath
				};
				mediaFiles.push(newFile);
			});
		} else if (curriculumData.mediaFiles) {
			mediaFiles = curriculumData.mediaFiles;
		}

		if (curriculumData.curriculumThemeId) {
			const curriculumTheme = await CurriculumThemes.findOneAsync(curriculumData.curriculumThemeId);
			if (curriculumTheme) curriculumData.selectedGroups = curriculumTheme.selectedGroups;
		}

		var id = await Curriculums.insertAsync({
			headline: curriculumData.headline,
			type: curriculumData.type,
			message: curriculumData.message || curriculumData.notes,
			materials: curriculumData.materials,
			homework: curriculumData.homework,
			scheduledDate: DateTimeUtils.getDatestampInTimezone(curriculumData.scheduledDate, (await Orgs.current()).getTimezone()),
			selectedGroups: curriculumData.selectedGroups,
			selectedStandards: curriculumData.selectedStandards,
			selectedTypes: curriculumData.selectedTypes,
			selectedAgeGroup: curriculumData.selectedAgeGroup,
			curriculumThemeId: curriculumData.curriculumThemeId,
			originalCurriculumId: curriculumData.originalCurriculumId,
			teacherNotes: curriculumData.teacherNotes,
			internalNotes: curriculumData.internalNotes,
			internalLink: curriculumData.internalLink,
			orgId: currentUser.orgId,
			createdBy: currentUser._id,
			createdAt: Date.now(),
			mediaFiles: mediaFiles
		});
	},
	'copyCurriculum': async function (copyCurriculumData) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();

		var currentCurriculum = await Curriculums.findOneAsync({ orgId: currentUser.orgId, _id: copyCurriculumData.sourceId });
		if (currentCurriculum) {
			currentCurriculum.createdBy = currentUser._id;
			currentCurriculum.scheduledDate = copyCurriculumData.scheduledDate;
			delete currentCurriculum._id;
			await Curriculums.insertAsync(currentCurriculum);
		}
	},
	'updateCurriculum': async function (curriculumData) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		await CurriculumUtils.updateCurriculum(curriculumData, currentUser, (await Orgs.current()).getTimezone(), Meteor.isServer);
	},
	'moveCurriculum': async function (options) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		await CurriculumUtils.moveCurriculum(options, currentUser);
	},
	'deleteCurriculum': async function (curriculumId) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		await CurriculumUtils.deleteCurriculum(curriculumId, currentUser);
	},
	'insertReservation': async function (reservationData, orgId = null, fromRegistration = false) {
		let historyPerformedByName = null;
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "reservations", action: "edit" }],
				evaluator: (person) => person.type == "admin" || person.type == "staff",
				throwError: true
			});
		} else if (fromRegistration) {
			historyPerformedByName = HistoryAuditPeoplePerformedByNames.PLR;
		}

		const currentUser = orgId ? null : await Meteor.userAsync();
		if (!orgId && (!currentUser ||
			((await currentUser?.fetchPerson())?.type != "admin" && reservationData.reservationType == "staff")))
			throw new Meteor.Error(403, "Access denied");

		const selectedPeople = _.flatten([reservationData.selectedPerson]);
		const currentOrg = await Orgs.findOneAsync({ _id: orgId || currentUser.orgId });
		logger.info('insertReservation > selectedPeople', selectedPeople);

		let responseMessage = "", allSuccessful = true, allowOverlapOverride = false, newReservationId = null;

		if (reservationData.scheduledDate && !moment(reservationData.scheduledDate).isValid()) {
			throw new Meteor.Error(500, "Scheduled date is not in the correct format.");
		}

		logger.info('insertReservation > reservationData', reservationData);
		logger.info('inserReservation > timezone', currentOrg.getTimezone());

		const adjustedScheduledDate = reservationData.scheduledDate && DateTimeUtils.getDatestampInTimezone(reservationData.scheduledDate, currentOrg.getTimezone());
		const adjustedScheduledEndDate = reservationData.scheduledEndDate && DateTimeUtils.getDatestampInTimezone(reservationData.scheduledEndDate, currentOrg.getTimezone());

		logger.info('insertReservation > adjustedScheduledDate', { 'adjustedScheduledDate': adjustedScheduledDate });
		logger.info('insertReservation > adjustedScheduledEndDate', { 'adjustedScheduledEndDate': adjustedScheduledEndDate });

		for (const p of selectedPeople) {
			const selectedPerson = await People.findOneAsync({ orgId: currentOrg._id, _id: p });
			if (!selectedPerson) throw new Meteor.Error(500, "Invalid person specified.");
			logger.info('insertReservation > selectedPerson', selectedPerson);

			//look for conflicts
			if (reservationData.recurringFrequency) {
				if (reservationData.closePriors) {
					const newScheduledEndDate = new moment(adjustedScheduledDate).add(-1, "days").valueOf();
					logger.info('insertReservation > Closing prior reservations', { personId: p, endDate: newScheduledEndDate });
					const reservationsToUpdate = await Reservations.find({ orgId: currentOrg._id, selectedPerson: p, scheduledDate: { "$lt": adjustedScheduledDate }, scheduledEndDate: null, recurringFrequency: { "$exists": true } }).fetchAsync();
					logger.info('insertReservation > Found reservations to update', { count: reservationsToUpdate.length, personId: p });
					const updateQuery = { "$set": { "scheduledEndDate": newScheduledEndDate } };
					for (const reservation of reservationsToUpdate) {
						if (Meteor.isServer) {
							const historyOptions = {
								changeType: HistoryAuditChangeTypes.EDIT,
								performedByUser: currentUser,
								performedByName: historyPerformedByName,
								previousState: reservation,
								currentState: { ...reservation, scheduledEndDate: newScheduledEndDate },
								callbackString: HistoryAuditRecordTypes.SCHEDULE
							};
							Meteor.callAsync('logHistory', historyOptions).then((result) => {
								Log.info("History record created successfully:", result);
							}).catch((error) => {
								Log.info("Error creating history record for schedule update", error);
							});
						}

						await Reservations.updateAsync({ _id: reservation._id }, updateQuery);
					}
				}

				let recurrenceSuccess = true;
				const existingRecurringReservations = await Reservations.find({ orgId: currentOrg._id, selectedPerson: p, recurringFrequency: { "$exists": true } }).fetchAsync();
				logger.info('insertReservation > Checking recurring conflicts', { personId: p, existingCount: existingRecurringReservations.length, scheduledDate: adjustedScheduledDate });
				_.each(existingRecurringReservations, rr => {
					const scheduleDateAfterStart = adjustedScheduledDate >= rr.scheduledDate
					const scheduleDateBeforeEnd = (!rr.scheduledEndDate || adjustedScheduledDate <= rr.scheduledEndDate)
					const scheduleEndDateAfterStart = adjustedScheduledEndDate >= rr.scheduledDate
					const scheduleEndDateBeforeEnd = (!rr.scheduledEndDate || adjustedScheduledEndDate <= rr.scheduledEndDate)
					const coincides = (scheduleDateAfterStart && scheduleDateBeforeEnd) || (scheduleEndDateAfterStart && scheduleEndDateBeforeEnd)
					logger.info('insertReservation > Found coinciding reservation', { reservationId: rr._id, personId: p, scheduleType: rr.scheduleType });
					if (coincides && adjustedScheduledDate > rr.scheduledDate && !rr.scheduledEndDate && rr.scheduleType == reservationData.scheduleType && !_.isEmpty(_.intersection(rr.recurringDays, reservationData.recurringDays))) {
						recurrenceSuccess = false; responseMessage = `${selectedPerson.firstName} ${selectedPerson.lastName} has an open-ended schedule entry of that type that conflicts with this new entry.`;
					}
					if (!reservationData.overrideOverlap && recurrenceSuccess && coincides && !_.isEmpty(_.intersection(rr.recurringDays, reservationData.recurringDays)) && rr.scheduleType === reservationData.scheduleType) {
						allowOverlapOverride = true; recurrenceSuccess = false; responseMessage += `${selectedPerson.firstName} ${selectedPerson.lastName} already has an overlapping schedule entry of that type for selected days.`;
					}
				});
				if (!recurrenceSuccess) {
					allSuccessful = false;
					continue;
				}
			}

			if (reservationData.recurringProfileSchedule) {
				const reservationsToRemove = await Reservations.find({ selectedPerson: p, recurringProfileSchedule: true }).fetchAsync();
				logger.info('insertReservation > Found recurring profile schedules', { count: reservationsToRemove.length, personId: p });
				for (const reservation of reservationsToRemove) {
					if (Meteor.isServer) {
						const historyOptions = {
							changeType: HistoryAuditChangeTypes.DELETE,
							performedByUser: currentUser,
							performedByName: historyPerformedByName,
							previousState: reservation,
							currentState: null,
							callbackString: HistoryAuditRecordTypes.SCHEDULE
						};

						Meteor.callAsync('logHistory', historyOptions).then((result) => {
							Log.info("History record created successfully:", result);
						}).catch((error) => {
							Log.info("Error creating history record for schedule deletion", error);
						});
					}
				}
				await Reservations.removeAsync({ _id: reservation._id });
			}

			const existingReservations = await Reservations.find({
				selectedPerson: p,
				cancellationReason: null,
				scheduleType: reservationData.scheduleType
			}).fetchAsync();

			logger.info('insertReservation > existingReservation', { existingReservations });
			// Check if new reservation conflicts
			let overlapResponse;
			if (!reservationData.overrideOverlap) {
				_.each(existingReservations, existingReservation => {
					overlapResponse = ScheduleUtils.validateReservations(existingReservation, reservationData, responseMessage, currentOrg, allSuccessful, selectedPerson)
					responseMessage = overlapResponse.responseMessage;
					allSuccessful = overlapResponse.allSuccessful;
					if (!allSuccessful) { // Don't keep checking if we've found a conflict
						return;
					}
				})
			}

			// Check if schedule types are the same and times are not the same/overlap
			if (allSuccessful || reservationData.overrideOverlap) {
				//throw new Meteor.Error(500, "A reservation already exists for that person on that date.");

				let reservationItem = {
					scheduledDate: adjustedScheduledDate,
					scheduledTime: reservationData.scheduledTime,
					scheduledEndTime: reservationData.scheduledEndTime,
					reservationType: _.contains(["person", "staff"], reservationData.reservationType) ? reservationData.reservationType : "person",
					selectedPerson: p,
					//scheduledLength: reservationData.scheduledLength,
					orgId: currentOrg._id,
					createdBy: currentUser ? currentUser._id : 'SYSTEM',
					scheduleType: reservationData.scheduleType,
					generatedFromBillingCharge: reservationData.generatedFromBillingCharge,
					enrolledItemId: reservationData.enrolledItemId
				};

				if (reservationData.hasOwnProperty('cancellationReasonId')) {
					reservationItem.cancellationReasonId = reservationData.cancellationReasonId;
				}

				if (adjustedScheduledDate) reservationItem.scheduledDate = adjustedScheduledDate;
				if (reservationData.recurringFrequency && isNaN(reservationData.recurringFrequency)) {
					throw new Meteor.Error(500, "Frequency value invalid for recurrence");
				} else if (reservationData.recurringFrequency) {
					reservationItem.recurringFrequency = parseInt(reservationData.recurringFrequency);
					reservationItem.recurringType = "weekly";
					reservationItem.recurringDays = reservationData.recurringDays;
					reservationItem.recurringProfileSchedule = reservationData.recurringProfileSchedule;
					if (adjustedScheduledEndDate) {
						reservationItem.scheduledEndDate = adjustedScheduledEndDate;
					}
				}
				if (reservationData.groupId !== "default") {
					reservationItem.groupId = reservationData.groupId;
				}

				logger.info('insertReservation > reservationItem', reservationItem);

				const reservationId = await Reservations.insertAsync(reservationItem);
				newReservationId = reservationId;

				// This is where the enrolling in billing plan happens in insertReservation if there's a linked schedule
				if (reservationData.linkedPlan && reservationItem.recurringFrequency) {
					try {
						reservationData.linkedPlan.personId = selectedPerson._id;
						reservationData.linkedPlan.reservationId = reservationId;
						logger.info('insertReservation > enrollBillingPlan', { reservationData, "linkedPlan": reservationData.linkedPlan, orgId });
						await enrollBillingPlan(reservationData.linkedPlan, orgId);
					} catch (e) {
						responseMessage = `Error creating linked billing plan for ${selectedPerson.firstName} ${selectedPerson.lastName}: ${e.message}`;
						allSuccessful = false;
						newReservationId = null;
						await Reservations.removeAsync({ _id: reservationId });
						continue;
					}
				}
				if (Meteor.isServer) {
					const reservation = await Reservations.findOneAsync({ _id: reservationId });

					if (reservation) {
						const historyOptions = {
							changeType: HistoryAuditChangeTypes.ADD,
							performedByUser: currentUser,
							performedByName: historyPerformedByName,
							previousState: null,
							currentState: reservation,
							callbackString: HistoryAuditRecordTypes.SCHEDULE
						};

						Meteor.callAsync('logHistory', historyOptions).then((result) => {
							Log.info("History record created successfully:", result);
						}).catch((error) => {
							Log.info("Error creating history record for schedule insert", error);
						});
					}

					await Meteor.callAsync('updateZkTecoForRelationships', p);
					Meteor.defer(async function () {
						await Meteor.callAsync('syncCrmStatusFromSchedules', p);
					});
				}
				responseMessage += "Scheduling item created for " + selectedPerson.firstName + " " + selectedPerson.lastName + " on " + new moment(reservationData.scheduledDate).tz(currentOrg.timezone).format("MM/DD/YY");
			} else {
				responseMessage += `A scheduling item already exists or overlaps for ${selectedPerson.firstName} ${selectedPerson.lastName} `
				if (reservationData.scheduledEndDate) {
					responseMessage += `between ${new moment(reservationData.scheduledDate).tz(currentOrg.timezone).format("MM/DD/YY")} and ${new moment(reservationData.scheduledEndDate).tz(currentOrg.timezone).format("MM/DD/YY")}`
				} else {
					if (reservationData.recurringFrequency) {
						responseMessage += `after ${new moment(reservationData.scheduledDate).tz(currentOrg.timezone).format("MM/DD/YY")}`
					} else {
						responseMessage += `on ${new moment(reservationData.scheduledDate).tz(currentOrg.timezone).format("MM/DD/YY")}`
					}
				}
				allSuccessful = false;
			}
		};

		if (responseMessage || !allSuccessful)
			logger.info('insertReservation > return', { responseMessage, allSuccessful, allowOverlapOverride, newReservationId });
		return {
			responseMessage,
			allSuccessful,
			allowOverlapOverride,
			newReservationId
		};

	},
	'reviewReservation': async function ({ reservationId, personId }) {
		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson?.();
		const currentReservation = await Reservations.findOneAsync(reservationId);
		if (!currentPerson) {
			throw new Meteor.Error(403, "Access denied");
		}
		const orgsScope = await currentPerson.findScopedOrgs();
		const orgsScopeList = orgsScope && _.pluck(orgsScope, "_id");

		if (!reservationId && personId) {
			await People.updateAsync({ _id: personId, orgId: { "$in": orgsScopeList } }, { $set: { needsBillingReview: false } });
		} else {
			if (Meteor.isServer && currentReservation) {
				const historyOptions = {
					changeType: HistoryAuditChangeTypes.EDIT,
					performedByUser: currentUser,
					previousState: currentReservation,
					currentState: { ...currentReservation, needsBillingReview: false },
					callbackString: HistoryAuditRecordTypes.SCHEDULE
				};

				Meteor.callAsync('logHistory', historyOptions).then((result) => {
					Log.info('History record created successfully:', result);
				}).catch((error) => {
					Log.info("Error creating history record for schedule update", error);
				});
			}
			await Reservations.updateAsync({ _id: reservationId, orgId: { "$in": orgsScopeList } }, { $set: { needsBillingReview: false } });
		}

	},
	'updateReservation': async function (reservationData, fromRegistration = false) {
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "reservations", action: "edit" }],
				evaluator: (person) => person.type == "admin" || person.type == "staff",
				throwError: true
			});
		}
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();
		const currentReservation = await Reservations.findOneAsync(reservationData.reservationId);

		if (!currentUser || !(currentUser.orgId == currentReservation?.orgId)) {
			throw new Meteor.Error(403, "Access denied");
		}

		const adjustedScheduledDate = reservationData.scheduledDate && DateTimeUtils.getDatestampInTimezone(reservationData.scheduledDate, (await Orgs.current()).getTimezone());
		const adjustedScheduledEndDate = reservationData.scheduledEndDate && DateTimeUtils.getDatestampInTimezone(reservationData.scheduledEndDate, (await Orgs.current()).getTimezone());
		//look for conflicts
		if (reservationData.recurringFrequency) {
			const existingRecurringReservations = await Reservations.find({ _id: { "$ne": reservationData.reservationId }, orgId: currentUser.orgId, selectedPerson: reservationData.selectedPerson, recurringFrequency: { "$exists": true } }).fetchAsync();
			_.each(existingRecurringReservations, rr => {
				const coincides = rr.scheduledDate <= adjustedScheduledDate && (!rr.scheduledEndDate || rr.scheduledEndDate > adjustedScheduledDate);
				if (coincides && adjustedScheduledDate > rr.scheduledDate && !rr.scheduledEndDate && rr.scheduleType == reservationData.scheduleType && !_.isEmpty(_.intersection(rr.recurringDays, reservationData.recurringDays))) {
					throw new Meteor.Error(500, "There is an open-ended schedule entry that conflicts with this new entry.");
				}
				if (coincides && !_.isEmpty(_.intersection(rr.recurringDays, reservationData.recurringDays)) && ((rr.scheduleType == reservationData.scheduleType && !rr.linkedPlan && !reservationData.linkedPlan) || (rr.linkedPlan && reservationData.linkedPlan && rr.linkedPlan.plan === reservationData.linkedPlan.plan))) {
					throw new Meteor.Error(500, "There is already an overlapping schedule entry of that type for selected days.");
				}
			});
		}

		// Check if new reservation conflicts
		const existingReservation = await Reservations.findOneAsync({
			selectedPerson: reservationData.selectedPerson,
			cancellationReason: null,
			scheduleType: reservationData.scheduleType
		});
		const overlapResponse = ScheduleUtils.validateReservations(existingReservation, reservationData, '', currentOrg, true, reservationData.selectedPerson)

		// Check if schedule types are the same and dates are not the same/overlap
		if (!((overlapResponse.overlapsDate && !overlapResponse.overlapsDaysOfTheWeek) || !overlapResponse.overlapsDate)) {
			throw new Meteor.Error(500, overlapResponse.responseMessage);
		}

		const currentPerson = await People.findOneAsync({ _id: reservationData.selectedPerson });
		if (currentOrg.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
			const currPlans = _.deep(currentPerson, 'billing.enrolledPlans');
			const todayDate = moment().tz(currentOrg.getTimezone()).valueOf();
			const linkedPlan = currentReservation.linkedPlan();
			if (linkedPlan && currPlans && currPlans.length) {
				const currPlansDate = [];

				if (currPlans && currPlans.length) {
					currPlans.forEach((plan) => {
						currPlansDate.push(moment.tz(plan.enrollmentDate, currentOrg.getTimezone()));
					});
				}

				const scheduleHasStarted = moment.min(currPlansDate).valueOf() < todayDate;
				const isFirstSchedule = moment.min(currPlansDate).valueOf() === linkedPlan.enrollmentDate;
				if (isFirstSchedule && !scheduleHasStarted) {
					Meteor.callAsync('updateEnrollmentDate', currentOrg.id, currentPerson._id, reservationData.scheduledDate, fromRegistration).then((res) => {
						Log.info('Enrollment date updated successfully');
					}).catch((err) => {
						Log.error('Could not update enrollment date', err);
					});
				}
			}
		}

		let reservationUpdateSet = {
			scheduledDate: adjustedScheduledDate,
			scheduledTime: reservationData.scheduledTime,
			scheduledEndTime: reservationData.scheduledEndTime,
			selectedPerson: reservationData.selectedPerson,
			scheduledLength: reservationData.scheduledLength,
			scheduledEndDate: null,
			scheduleType: reservationData.scheduleType,
			groupId: reservationData.groupId
		};
		if (reservationData.hasOwnProperty('generatedFromBillingCharge')) {
			reservationUpdateSet.generatedFromBillingCharge = reservationData.generatedFromBillingCharge;
		}
		if (reservationData.hasOwnProperty('cancellationReasonId')) {
			reservationUpdateSet.cancellationReasonId = reservationData.cancellationReasonId;
		}

		if (reservationData.recurringFrequency && isNaN(reservationData.recurringFrequency)) {
			throw new Meteor.Error(500, "Frequency value invalid for recurrence");
		} else if (reservationData.recurringFrequency) {
			reservationUpdateSet.recurringFrequency = parseInt(reservationData.recurringFrequency);
			reservationUpdateSet.recurringType = "weekly";
			reservationUpdateSet.recurringDays = reservationData.recurringDays
			if (adjustedScheduledEndDate) {
				reservationUpdateSet.scheduledEndDate = adjustedScheduledEndDate;
			} else if (adjustedScheduledDate < new moment().valueOf())
				throw new Meteor.Error(500, "Past recurring reservations require an end date.");
		}

		const unset = {};
		if (reservationUpdateSet.groupId === "default") {
			delete reservationUpdateSet.groupId;
			unset.groupId = 1;
		}

		if (reservationData.linkedPlan && reservationUpdateSet.recurringFrequency) {
			try {
				reservationData.linkedPlan.personId = reservationUpdateSet.selectedPerson;
				reservationData.linkedPlan.reservationId = reservationData.reservationId;
				await enrollBillingPlan(reservationData.linkedPlan, null, fromRegistration);
			} catch (e) {
				throw new Meteor.Error(500, `Error creating linked billing plan: ${e.message}`);
			}
		}

		if (reservationData.parentAdjusted) {
			reservationUpdateSet.parentAdjusted = reservationData.parentAdjusted;
			reservationUpdateSet.parentAdjustedAt = reservationData.parentAdjustedAt;
			reservationUpdateSet.parentAdjustedBy = reservationData.parentAdjustedBy;
		}

		if (_.isEmpty(unset)) {
			await Reservations.updateAsync(reservationData.reservationId, { $set: reservationUpdateSet });
		} else {
			await Reservations.updateAsync(reservationData.reservationId, { $set: reservationUpdateSet, $unset: unset });
		}

		if (Meteor.isServer && currentReservation) {
			const updatedReservation = await Reservations.findOneAsync({ _id: reservationData.reservationId });
			const historyOptions = {
				changeType: HistoryAuditChangeTypes.EDIT,
				performedByUser: currentUser,
				previousState: currentReservation,
				currentState: updatedReservation,
				callbackString: HistoryAuditRecordTypes.SCHEDULE
			}

			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for schedule update", error);
			});
		}

		// Find the linked plan (if any) and see if it is part of a bundle
		const person = await People.findOneAsync({ _id: reservationUpdateSet.selectedPerson });
		if (person) {
			const currentPlans = _.deep(person, 'billing.enrolledPlans');
			const linkedPlan = _.find(currentPlans, ep => ep.reservationId === reservationData.reservationId);
			// If linked plan is part of a bundle and the number of days has changed, recalculate the price of the bundle discount
			if (linkedPlan && linkedPlan.bundlePlanId && (currentReservation.recurringDays?.length ?? 0) !== (reservationData.recurringDays?.length ?? 0)) {
				await BillingUtils.recalculateBundleDiscount(person, linkedPlan.bundlePlanId);
			}
		}
		if (Meteor.isServer) {
			await Meteor.callAsync('updateZkTecoForRelationships', reservationData.selectedPerson);
			Meteor.defer(async function () {
				await Meteor.callAsync('syncCrmStatusFromSchedules', reservationData.selectedPerson);
			});
		}
	},
	// for the purposes of automatically updating the group without having to worry about throwing errors for no end date etc etc
	'updateReservationGroupOnly': async function (reservationId, groupId) {
		const currentUser = await Meteor.userAsync();
		const currentReservation = await Reservations.findOneAsync(reservationId);
		await Reservations.updateAsync(reservationId, { $set: { groupId: groupId } });
		if (Meteor.isServer && currentReservation) {
			const historyOptions = {
				changeType: HistoryAuditChangeTypes.EDIT,
				performedByUser: currentUser,
				previousState: currentReservation,
				currentState: { ...currentReservation, groupId: groupId },
				callbackString: HistoryAuditRecordTypes.SCHEDULE
			}
			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for schedule update", error);
			});
		}
	},
	'cancelReservation': async function (reservationCancelData) {
		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync(), currentPerson = await currentUser?.fetchPerson();
		const reservation = await Reservations.findOneAsync({
			"_id": reservationCancelData.reservationId,
			orgId: currentUser.orgId
		});
		if (!currentUser ||
			!reservation)
			throw new Meteor.Error(403, "Access denied");

		if (reservationCancelData.occurrenceTime && reservation.recurringFrequency) {
			let reservationData = await Reservations.findOneAsync({ _id: reservationCancelData.reservationId },
				{
					fields: {
						"recurringFrequency": 0,
						"recurringType": 0,
						"recurringDays": 0,
						"recurringExceptions": 0
					}
				});
			delete reservationData._id;
			reservationData.scheduledDate = reservationCancelData.occurrenceTime;
			reservationData.cancellationReason = reservationCancelData.selectedReason;
			reservationData.cancellationComments = reservationCancelData.comment;
			reservationData.cancellationDate = Date.now();
			reservationData.cancellationOriginalReservationId = reservationCancelData.reservationId;
			reservationData.createdBy = "SYSTEM-CANCEL-RECURRING-INSTANCE";
			reservationData.createdAt = Date.now();

			await Reservations.updateAsync(reservationCancelData.reservationId, { $push: { recurringExceptions: reservationCancelData.occurrenceTime } });
			await Reservations.insertAsync(reservationData);
		} else {
			const updateData = {
				cancellationReason: reservationCancelData.selectedReason,
				cancellationComments: reservationCancelData.comment,
				cancellationDate: Date.now()
			};
			if (reservation.cancellationReason) {
				updateData.updatedBy = currentPerson._id;
				updateData.updatedAt = Date.now()
			} else {
				updateData.createdBy = currentPerson._id;
				updateData.createdAt = Date.now()
			}
			await Reservations.updateAsync(reservationCancelData.reservationId, { $set: updateData });
			if (reservation.recurrenceId) {
				await Reservations.updateAsync({ "_id": reservation.recurrenceId }, { $push: { recurringExceptions: reservation.scheduledDate } });
			}
		}
		if (Meteor.isServer) {
			await Meteor.callAsync('updateZkTecoForRelationships', reservation.selectedPerson);
			Meteor.defer(async function () {
				await Meteor.callAsync('syncCrmStatusFromSchedules', reservation.selectedPerson);
			});
		}
	},
	'deleteReservation': async function (reservationId) {
		await processPermissions({
			assertions: [{ context: "reservations", action: "edit" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});
		const currentUser = await Meteor.userAsync();
		const currentOrg = await Orgs.current();

		const existingReservation = await Reservations.findOneAsync(reservationId);

		if (!currentUser || !(currentUser.orgId == existingReservation.orgId)) {
			throw new Meteor.Error(403, "Access denied");
		}

		if (existingReservation.recurringFrequency && existingReservation.scheduledDate < new moment.tz(currentOrg.getTimezone()).startOf("day").valueOf()) {
			throw new Meteor.Error(500, "A past recurring schedule cannot be deleted.");
		}

		if (Meteor.isServer) {
			const currentState = await Reservations.findOneAsync({ _id: reservationId });
			const historyOptions = {
				changeType: HistoryAuditChangeTypes.DELETE,
				performedByUser: currentUser,
				previousState: currentState,
				currentState: null,
				callbackString: HistoryAuditRecordTypes.SCHEDULE
			};

			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for schedule deletion", error);
			});
		}

		await Reservations.removeAsync(reservationId);

		const person = await People.findOneAsync({ _id: existingReservation.selectedPerson })
		const plan = _.find(_.deep(person, 'billing.enrolledPlans'), p => p.reservationId === existingReservation._id);
		if (plan && plan.bundlePlanId) {
			await BillingUtils.recalculateBundleDiscount(person, plan.bundlePlanId);
		}

		await People.updateAsync({
			orgId: currentOrg._id,
			"_id": existingReservation.selectedPerson,
		}, {
			"$pull": {
				"billing.enrolledPlans": {
					"reservationId": existingReservation._id
				}
			}
		});

		if (Meteor.isServer) {
			await Meteor.callAsync('updateZkTecoForRelationships', existingReservation.selectedPerson);
			Meteor.defer(async function () {
				await Meteor.callAsync('syncCrmStatusFromSchedules', existingReservation.selectedPerson);
			});
		}

	},
	'removeCancellation': async function (options) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			!((await currentUser?.fetchPerson())?.type == "admin"))
			throw new Meteor.Error(403, "Access denied");

		const reservation = await Reservations.findOneAsync({ orgId: currentUser.orgId, _id: options.reservationId });
		if (reservation) {
			await Reservations.removeAsync({ _id: options.reservationId });
			if (reservation.cancellationOriginalReservationId) {
				await Reservations.updateAsync({ _id: reservation.cancellationOriginalReservationId }, { "$pull": { recurringExceptions: reservation.scheduledDate } });
			}
		}
	},
	'insertFood': async function (foodData) {
		await processPermissions({
			assertions: [{ context: "food", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync(),
			currentOrg = await currentUser.fetchOrg();

		let foodItem = {
			meal: foodData.meal,
			description: foodData.description,
			selectedGroups: foodData.selectedGroups,
			orgId: currentUser.orgId,
			createdBy: currentUser._id
		};
		if (foodData.foodItems) foodItem.foodItems = foodData.foodItems;
		if (foodData.scheduledDate) foodItem.scheduledDate = DateTimeUtils.getDatestampInTimezone(foodData.scheduledDate, currentOrg.getTimezone());
		if (foodData.recurringFrequency && isNaN(foodData.recurringFrequency)) {
			throw new Meteor.Error(500, "Frequency value invalid for recurrence");
		} else if (foodData.recurringFrequency) {
			foodItem.recurringFrequency = parseInt(foodData.recurringFrequency);
			foodItem.recurringType = "weekly";
			foodItem.recurringDays = foodData.recurringDays;
			foodItem.scheduledEndDate = foodData.scheduledEndDate && DateTimeUtils.getDatestampInTimezone(foodData.scheduledEndDate, currentOrg.getTimezone());
		}

		await Foods.insertAsync(foodItem);
	},
	'updateFood': async function (foodData) {
		await processPermissions({
			assertions: [{ context: "food", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync(),
			currentOrg = await currentUser.fetchOrg();

		if (!(currentUser.orgId == (await Foods.findOneAsync(foodData.foodId)).orgId))
			throw new Meteor.Error(403, "Access denied");

		let foodUpdateSet = {
			meal: foodData.meal,
			description: foodData.description,
			selectedGroups: foodData.selectedGroups,
			foodItems: foodData.foodItems
		};
		if (foodData.scheduledDate) {
			foodUpdateSet.scheduledDate = DateTimeUtils.getDatestampInTimezone(foodData.scheduledDate, currentOrg.getTimezone());
		}
		if (foodData.recurringFrequency && isNaN(foodData.recurringFrequency)) {
			throw new Meteor.Error(500, "Frequency value invalid for recurrence");
		} else if (foodData.recurringFrequency) {
			foodUpdateSet.recurringFrequency = parseInt(foodData.recurringFrequency);
			foodUpdateSet.recurringType = "weekly";
			foodUpdateSet.recurringDays = foodData.recurringDays
			foodUpdateSet.scheduledEndDate = foodData.scheduledEndDate && DateTimeUtils.getDatestampInTimezone(foodData.scheduledEndDate, currentOrg.getTimezone());
		} else {
			foodUpdateSet.recurringFrequency = null;
			foodUpdateSet.recurringType = null;
			foodUpdateSet.recurringDays = null;
			foodUpdateSet.scheduledEndDate = null;
		}
		await Foods.updateAsync(foodData.foodId, { $set: foodUpdateSet });
	},
	'deleteFood': async function (foodId, occurrenceTime) {
		await processPermissions({
			assertions: [{ context: "food", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		if (!(currentUser.orgId == (await Foods.findOneAsync(foodId)).orgId))
			throw new Meteor.Error(403, "Access denied");

		if (occurrenceTime) {
			await Foods.updateAsync(foodId, { $push: { recurringExceptions: occurrenceTime } });
		} else {
			await Foods.removeAsync(foodId);
		}
	},
	"timelineLikeMoment": async function (options) {
		var currentUser = await Meteor.userAsync();
		var currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			!currentPerson.type == "family")
			throw new Meteor.Error(403, "Access denied");

		let momentId, likeType;
		if (typeof options === 'object' && options !== null) {
			momentId = options.momentId;
			likeType = options.likeType;
		} else
			momentId = options;

		var currentMoment = await Moments.findOneAsync(momentId);
		if (!currentMoment)
			throw new Meteor.Error(403, "Access denied");
		var activeRelationship = await Relationships.findOneAsync({ personId: currentPerson._id, targetId: { $in: currentMoment.taggedPeople } });
		if (!activeRelationship)
			throw new Meteor.Error(403, "Access denied");

		var setObject = {}, setValue = {};
		setValue.type = "like";
		setValue.createdAt = new Date().valueOf();
		setValue.personId = activeRelationship.targetId;
		setValue.sender = currentPerson.firstName + " " + currentPerson.lastName;
		if (likeType) setValue.likeType = likeType;

		setObject["reactions." + currentPerson._id] = setValue;
		await Moments.updateAsync({ _id: momentId }, { "$set": setObject });
		processAnalytics({
			engagementData: {
				orgId: currentMoment.orgId,
				type: "app",
				subType: "timeline_like",
				detail: currentMoment.momentType,
				momentId: currentMoment._id,
				createdBy: "SYSTEM",
				targetPersonId: activeRelationship.targetId,
				sourcePersonId: currentPerson._id
			},
			metaCxData: {
				type: 'moment-reaction',
				data: {
					orgId: currentMoment.orgId,
					orgName: (await Orgs.current()).name,
					personId: currentPerson._id,
					type: currentPerson.type,
					targetPersonId: activeRelationship.targetId,
					likeType: options.likeType
				}
			}
		});

	},
	'requestResetPassword': async function (passedEmailAddress) {
		if (Meteor.isServer) {
			const emailAddress = passedEmailAddress.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // $& means the whole matched string
			// Start the password reset flow for all users who match the email address case insensitively
			const currentUsers = await Meteor.users.find({ 'emails.address': { $regex: `^${emailAddress}$`, $options: 'i' } }, { readPreference: "secondaryPreferred" }).fetchAsync();
			for (const currentUser of currentUsers) {
				const currentOrg = await currentUser.fetchOrg();
				if (currentUser && !currentUser.pending) {
					const resetToken = tokenString();
					await Meteor.users.updateAsync(currentUser._id, { $set: { passwordResetToken: resetToken } });
					await processPasswordResetMail(passedEmailAddress, resetToken, currentOrg);
				} else if (currentUser && currentUser.pending) {
					throw new Meteor.Error(500, "It looks like that email has not yet been registered. Please contact your provider to request a new invitation.");
				} else {
					throw new Meteor.Error(404, "Sorry, it doesn't look like that email has been registered.");
				}
			}
		}
	},
	'processResetPassword': async function (requestData) {
		if (Meteor.isServer) {
			// Match all users who have the same email address case insensitively
			const currentUsers = await Meteor.users.find({ 'emails.address': { $regex: `^${requestData.emailAddress}$`, $options: 'i' } }, { readPreference: "secondaryPreferred" }).fetchAsync();

			if (!currentUsers) {
				throw new Meteor.Error('404', 'Sorry that email address couldn\'t be found');
			}

			let userFound = false;
			for (const currentUser of currentUsers) {
				/* Added in template rework */
				// Make sure the reset token matches this user and that a user hasn't already been found
				if (currentUser.passwordResetToken !== requestData.resetToken || userFound) {
					continue;
				}
				userFound = true;

				if (currentUser) {
					// Reset the password
					await Accounts.setPasswordAsync(currentUser._id, requestData.password);
					const cuser = await currentUser.fetchPerson();
					if (cuser && cuser.type === 'admin') {
						await Meteor.callAsync('sendManageUserUpdates', currentUser, false, false, requestData.password);
					}
				} else {
					throw new Meteor.Error('404', 'Sorry that email address couldn\'t be found');
				}
			}
			if (!userFound) {
				throw new Meteor.Error('403', 'That email and token combination could not be found');
			}
		}
	},
	'setUiOption': async function (option, value) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["hideAdminDashboardEngagementIntro", "hideAdminDashboardOnboarding", "attendanceReportSort", "peopleListFilters", "calendarFilters"], option))
			throw new Meteor.Error(403, "Access denied");
		var options = {};

		var optionKey = "uiOptions." + option;
		options[optionKey] = value;

		await Meteor.users.updateAsync({ _id: currentUser._id }, { $set: options });
	},
	'registerPushTokenSNS': async function (tokenObj, platformApplication, existingArn) {
		this.unblock()
		var currentUser = await Meteor.userAsync();
		if (!currentUser) throw new Meteor.Error(403, "Access denied");

		if (Meteor.isServer) {
			let createEndpointArn = false;
			let existingSnsArn = existingArn || null;
			const allSnsTokens = currentUser.services && currentUser.services.sns && currentUser.services.sns.deviceTokens
			if (allSnsTokens && !existingSnsArn) {
				// find an endpoint by push token -- need this for backend migration from PushWoosh
				let existingPushRecord = _.find(allSnsTokens, (sns) => { return sns.pushToken == tokenObj.token });
				if (existingPushRecord) {
					existingSnsArn = existingPushRecord.endpointArn;
				}
			}

			if (!existingSnsArn) createEndpointArn = true;

			//we still go through this path firt since it could error out on AWS side
			if (existingSnsArn) {
				try {
					const existingPlatformEndpoint = await SNS.getEndpointAttributes({ EndpointArn: existingSnsArn }).promise();
					const relationships = await Relationships.find({ personId: currentUser.personId }).fetchAsync()
					let isSubscriptionEnabledManually = false
					if (relationships && relationships.length > 0) {
						for (const rel of relationships) {
							if (rel.subscriptions) {
								Object.values(rel.subscriptions).find(subscription => {
									if (subscription.push === true) {
										isSubscriptionEnabledManually = true
									}
								});
							}
						}
					}
					const existingSnsArnFlag = existingPlatformEndpoint?.Attributes?.Enabled
					if ((existingSnsArnFlag != "true") && isSubscriptionEnabledManually) {
						createEndpointArn = true;
					} else {
						const params = {
							Attributes: {
								"Enabled": "true",
								"Token": tokenObj.token
							},
							EndpointArn: existingSnsArn
						};
						await SNS.setEndpointAttributes(params).promise();
						await Meteor.users.updateAsync({ _id: currentUser._id, 'services.sns.deviceTokens.endpointArn': existingSnsArn }, { $set: { 'services.sns.deviceTokens.$.pushToken': tokenObj.token } });

						return { arn: existingSnsArn };
					}
				} catch (e) {
					createEndpointArn = true;
				}
			}

			if (createEndpointArn) {
				const snsParams = {
					Token: tokenObj.token,
					PlatformApplicationArn: platformApplication,
				};
				const result = await SNS.createPlatformEndpoint(snsParams).promise();
				let endpointArn = result?.EndpointArn
				if (endpointArn) {
					const platformEndpoint = await SNS.getEndpointAttributes({ EndpointArn: endpointArn }).promise();
					const snsArnFlag = platformEndpoint?.Attributes?.Enabled
					if (snsArnFlag != "true") {
						const params = {
							Attributes: {
								"Enabled": "true",
								"Token": tokenObj.token
							},
							EndpointArn: endpointArn
						};
						await SNS.setEndpointAttributes(params).promise();
					}
				}
				const setUpdate = {
					pushToken: tokenObj.token,
					os: tokenObj.os,
					endpointArn: result.EndpointArn,
				};

				await Meteor.users.updateAsync({ _id: currentUser._id }, { $addToSet: { 'services.sns.deviceTokens': setUpdate } });
				return { arn: result.EndpointArn };
			}
		}
	},
	'registerPushToken': async function (pushToken, userId, appSource) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		if (!currentUser)
			throw new Meteor.Error(403, "Access denied");

		if (Meteor.isServer) {

			if (appSource && appSource == "rn") pushToken += "|RN";

			await Meteor.users.updateAsync(
				{ _id: currentUser._id },
				{ $addToSet: { 'services.pushwoosh.deviceTokens': pushToken } }
			);
		}
	},
	'unregisterSNSPushToken': async function (arn) {
		this.unblock();
		var currentUser = await Meteor.userAsync();
		if (!currentUser) {
			throw new Meteor.Error(403, "Access denied");
		}

		if (Meteor.isServer) {
			deleteEndpointArn(arn);
			await Meteor.users.updateAsync(
				{ _id: currentUser._id },
				{ $pull: { 'services.sns.deviceTokens': { endpointArn: arn } } },
			);
		}
	},
	'unregisterPushToken': async function (pushToken, userId, appSource) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser)
			throw new Meteor.Error(403, "Access denied");

		if (Meteor.isServer) {

			if (appSource && appSource == "rn") pushToken += "|RN";

			await Meteor.users.updateAsync(
				{ _id: currentUser._id },
				{ $pull: { 'services.pushwoosh.deviceTokens': pushToken } }
			);
		}
	},
	'insertMomentDefinition': async function (momentTypePretty) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			currentPerson?.type != "admin" ||
			!currentPerson?.superAdmin)
			throw new Meteor.Error(403, "Access Denied");

		var templateDefinition = MomentDefinitions.templateDefinition();
		templateDefinition.momentTypePretty = momentTypePretty;
		const momentDefinitionId = await MomentDefinitions.insertAsync(templateDefinition);
		return momentDefinitionId;
	},
	'updateChildcareCrmDefinition': async function (curId, definitionData) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			currentPerson?.type != "admin" ||
			!currentPerson?.superAdmin)
			throw new Meteor.Error(403, "Access Denied");

		const crmAccountId = await ChildcareCrmAccounts.updateAsync({ _id: curId }, definitionData);
		return crmAccountId;
	},
	'updateMomentDefinition': async function (curId, definitionData) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			currentPerson?.type != "admin" ||
			!currentPerson?.superAdmin)
			throw new Meteor.Error(403, "Access Denied");

		const momentDefinitionId = await MomentDefinitions.updateAsync({ _id: curId }, definitionData);
		return momentDefinitionId;
	},
	"addMomentTypeToCustomer": async function (curId, momentTypeName) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			currentPerson?.type != "admin" ||
			!currentPerson?.superAdmin)
			throw new Meteor.Error(403, "Access Denied");

		await Orgs.updateAsync({ _id: curId }, { $addToSet: { enabledMomentTypes: momentTypeName } });
	},
	"deleteMomentTypeFromCustomer": async function (curId, momentTypeName) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			currentPerson?.type != "admin" ||
			!currentPerson?.superAdmin)
			throw new Meteor.Error(403, "Access Denied");

		await Orgs.updateAsync({ _id: curId }, { $pull: { enabledMomentTypes: momentTypeName } });
	},
	"updateCarePlanDefinition": async function (curId, carePlanDefinition) {
		var currentUser = await Meteor.userAsync();
		const currentPerson = await currentUser?.fetchPerson();
		if (!currentUser ||
			currentPerson?.type != "admin" ||
			!currentPerson?.superAdmin)
			throw new Meteor.Error(403, "Access Denied");

		await Orgs.updateAsync({ _id: curId }, { $set: { carePlanDefinition: carePlanDefinition } });
	},
	"updateCarePlanValue": async function (personId, fieldId, newValue) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			(await currentUser.fetchPerson())?.type != "admin")
			throw new Meteor.Error(403, "Access Denied");

		var fieldToModify = _.chain((await Orgs.current()).carePlanDefinition.carePlanSections)
			.pluck("fields").flatten()
			.findWhere({ dataId: fieldId })
			.value();

		if (fieldToModify) {

			var query, auditValue;

			if (fieldToModify.fieldType == "attachments") {

				if (newValue.action && newValue.action == "remove") {
					query = { $pull: {} };
					query["$pull"]["carePlan." + fieldId] = { "mediaToken": newValue.mediaToken };
					auditValue = newValue.mediaToken;

				} else {
					query = { $addToSet: {} };
					query["$addToSet"]["carePlan." + fieldId] = newValue;
					auditValue = newValue;
				}
			} else {
				query = { $set: {} };
				query["$set"]["carePlan." + fieldId] = newValue;
				auditValue = newValue;
			}

			await People.updateAsync({ _id: personId }, query);

			if (Meteor.isServer) {
				var auditData = {
					targetPersonId: personId,
					fieldId: fieldId,
					value: auditValue,
					action: newValue.action ? newValue.action : "update"
				};
				await Meteor.callAsync("insertAuditCarePlan", auditData);
			}

		}

	},
	"updateProfileFieldValue": async function (personId, fieldPath, newValue) {
		//embedded permission check below
		var currentUser = await Meteor.userAsync();
		const person = await People.findOneAsync({ orgId: currentUser.orgId, _id: personId }),
			userPerson = await currentUser?.fetchPerson();
		if (!currentUser || !person)
			throw new Meteor.Error(403, "Access Denied");

		const currentOrg = await Orgs.current();
		if (fieldPath.fieldId == "pinCode") {
			if (!/^[0-9]+$/.test(newValue)) throw new Meteor.Error(500, "PIN Code must be a number");
			const pinLength = currentOrg.pinLength()
			if (newValue.length !== pinLength) {
				throw new Meteor.Error(500, `PIN Code must be ${pinLength} digits`);
			}
			let existsPin = false;
			if (currentOrg.pinUniqueAcrossOrgs()) {
				const allRelatedOrgIds = await currentOrg.findAllRelatedOrgIds();
				const allOrgIds = allRelatedOrgIds.concat([currentUser.orgId]);
				existsPin = await People.findOneAsync({ orgId: { $in: allOrgIds }, pinCode: newValue });
			} else {
				existsPin = await People.findOneAsync({ orgId: currentUser.orgId, pinCode: newValue });
			}
			if (existsPin || currentOrg.kioskPinCode == newValue) throw new Meteor.Error(500, "PIN Code already in use for another user");
		}

		if (fieldPath.fieldId == "pinCodeSupplemental") {
			if (!/^[0-9]+$/.test(newValue)) throw new Meteor.Error(500, "Supplemental PIN must be a number");
			if (newValue.length > 15) throw new Meteor.Error(500, "Supplemental PIN must be 15 or less digits");
			const existsPin = await People.findOneAsync({ orgId: currentUser.orgId, pinCodeSupplemental: newValue });
			if (existsPin) throw new Meteor.Error(500, "Supplemental PIN already in use for another user");
		}

		if (fieldPath.fieldPath && fieldPath.fieldPath.startsWith("/")) fieldPath.fieldPath = fieldPath.fieldPath.substring(1);

		let dbFieldPath = fieldPath.fieldId;

		dbFieldPath =
			(fieldPath.fieldPath ? fieldPath.fieldPath.replace("/", ".") + "." : "") +
			(parseInt(fieldPath.fieldIndex) >= 0 ? parseInt(fieldPath.fieldIndex) + "." : "") +
			fieldPath.fieldId;

		const defFieldPath = dbFieldPath.replace(/\.\d+\.?/gi, '.');

		let currentFieldGroup = await person.allowableProfileFields(), defField;

		defFieldPath.split(".").forEach((defFieldName) => {
			defField = _.find(currentFieldGroup, (cf) => { return cf.name == defFieldName; });
			if (defField.type == "fieldGroup") {
				currentFieldGroup = defField.fields;
			}
		});
		if ((currentOrg.profileDataPrefix() || person.profileData) && fieldPath.fieldId != "pinCode" && fieldPath.fieldId != "pinCodeSupplemental")
			dbFieldPath = (currentOrg.profileDataPrefix() || "profileData") + "." + dbFieldPath;

		if (defField) {
			let familyAuthorized = false;
			if (defField.isFamilyEditable && (
				(userPerson.type == "family" && userPerson._id == person._id) ||
				(userPerson.type == "family" && person.type == "person" && await Relationships.findOneAsync({ orgId: userPerson.orgId, personId: userPerson._id, targetId: person._id, relationshipType: "family" }))
			))
				familyAuthorized = true;

			if (!familyAuthorized) {
				await processPermissions({
					assertions: [{ context: "people/profile", action: "edit" }],
					evaluator: (thisPerson) => thisPerson.type == "admin" || (thisPerson.type == "staff" && thisPerson._id == person._id),
					throwError: true
				});
			}

			var query, auditValue;

			if (defField.type == "attachments") {
				if (newValue.action && newValue.action == "remove") {
					query = { $pull: {} };
					query["$pull"][dbFieldPath] = { "mediaToken": newValue.mediaToken };
				} else {
					query = { $addToSet: {} };
					query["$addToSet"][dbFieldPath] = newValue;
				}
			} else if (defField.type == "file") {
				query = { $set: {} };
				query["$set"][dbFieldPath] = newValue.mediaUrl;
			} else if (defField.type == "date") {
				const newDate = new moment.tz(newValue, "MM-DD-YYYY", (await Orgs.current()).getTimezone());
				if (!newDate.isValid()) throw new Meteor.Error(500, "Invalid date format.");
				query = { $set: {} };
				query["$set"][dbFieldPath] = newDate.startOf("day").valueOf();
			} else {
				query = { $set: {} };
				query["$set"][dbFieldPath] = newValue;
			}

			await People.updateAsync({ _id: personId }, query);
		}
	},
	"clearDateProfileFieldValue": async function (personId, fieldPath) {
		var currentUser = await Meteor.userAsync();
		const person = await People.findOneAsync({ orgId: currentUser.orgId, _id: personId }),
			userPerson = await currentUser?.fetchPerson();
		if (!currentUser || !person) {
			throw new Meteor.Error(403, "Access Denied");
		}

		await processPermissions({
			assertions: [{ context: "people/profile", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || (thisPerson.type == "staff" && thisPerson._id == person._id),
			throwError: true
		});

		const currentOrg = await Orgs.current();
		let dbFieldPath = fieldPath.fieldId;

		dbFieldPath =
			(fieldPath.fieldPath ? fieldPath.fieldPath.replace("/", ".") + "." : "") +
			(parseInt(fieldPath.fieldIndex) >= 0 ? parseInt(fieldPath.fieldIndex) + "." : "") +
			fieldPath.fieldId;

		if ((currentOrg.profileDataPrefix() || person.profileData) && fieldPath.fieldId != "pinCode" && fieldPath.fieldId != "pinCodeSupplemental")
			dbFieldPath = (currentOrg.profileDataPrefix() || "profileData") + "." + dbFieldPath;

		let query = { $unset: {} };
		query["$unset"][dbFieldPath] = 1;
		await People.updateAsync({ _id: personId }, query);
	},
	"insertBillingPlanBundle": async function (planData) {
		await processPermissions({
			assertions: [{ context: 'billing/configuration/plans', action: 'edit' }],
			evaluator: (person) => person.type === 'admin',
			throwError: true
		});

		const org = await Orgs.current();
		const currentUser = await Meteor.userAsync();

		let billingPlanBundle = {
			_id: Random.id(),
			type: planData.type
		};

		billingPlanBundle.plans = [];
		const plans = [];
		for (let i = 0; i < 2; i++) {
			const plan = _.find(_.deep(org, 'billing.plansAndItems'), p => p._id === planData[`plan_${i}`]);
			if (!plan) {
				throw new Meteor.Error('400', 'Invalid plan');
			}
			billingPlanBundle.plans.push(plan._id);
			plans.push(plan);
		}
		if (plans[0]._id === plans[1]._id) {
			throw new Meteor.Error('400', 'The plans cannot be the same');
		}
		const existingBundles = _.filter(_.deep(org, 'billing.plansAndItems'), p => p.type === PLAN_BUNDLE_TYPE && (p.plans.includes(plans[0]._id) && p.plans.includes(plans[1]._id)));
		if (existingBundles.length) {
			throw new Meteor.Error('400', 'This bundle already exists.');
		}
		billingPlanBundle.description = plans.map(p => p.description).join(' and ');

		billingPlanBundle.scaledAmounts = [];
		let amount = '0.00';

		const plan1Max = plans[0].requiredEnrollmentMax ?? 5;
		const plan2Max = plans[1].requiredEnrollmentMax ?? 5;
		const maxDays = Math.max(plan1Max, plan2Max);

		for (let i = 0; i < maxDays; i++) {
			const amounts = [];
			for (let j = 0; j < maxDays; j++) {
				if (planData[`scaledAmount_${i}_${j}`] === undefined) {
					amount = null;
				} else {
					amount = parseFloat(planData[`scaledAmount_${i}_${j}`] ?? 'a');
				}
				try {
					if (amount !== null) {
						check(amount, Number);
					}
				} catch (e) {
					const dayCount = i + 1;
					throw new Meteor.Error('400', `Invalid amount for ${dayCount} Day/Wk`);
				}
				amounts.push(amount)
			}
			billingPlanBundle.scaledAmounts.push(amounts);
		}
		if (org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
			billingPlanBundle.regFeeExempt = planData.regFeeExempt;
			billingPlanBundle.siblingDiscountExempt = planData.siblingDiscountExempt;
			billingPlanBundle.designations = planData.designations;
		}

		try {
			let programDetails = BillingUtils.validateProgramDetails(planData.programDetails);
			if (programDetails || programDetails === '') {
				billingPlanBundle.programDetails = programDetails;
			}
		} catch (e) {
			throw new Meteor.Error('400', e.message);
		}

		await Orgs.updateAsync({ _id: org._id }, { $push: { 'billing.plansAndItems': billingPlanBundle } });

		Meteor.defer(Meteor.bindEnvironment(() => {
			Meteor.callAsync('trackSettingChange', {
				user: currentUser,
				org,
				dataType: 'billing.plansAndItems',
				value: { _id: billingPlanBundle._id }
			}).then((result) => {
				Log.info("Setting change tracked successfully:", result);
			}).catch((error) => {
				Log.error("Error tracking setting change", error);
			});
		}));
	},
	"insertBillingPlan": async function (planData) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const org = await Orgs.current();
		const currentUser = await Meteor.userAsync();
		if (org.hasCustomization(AvailableCustomizations.REQUIRE_LEDGER_ACCOUNT) && !planData.ledgerAccountName) {
			throw new Meteor.Error(500, "A ledger account must be assigned before this plan can be saved");
		}

		let amount = planData.amount;

		let billingPlan = {
			_id: Random.id(),
			description: planData.description,
			type: planData.type,
			program: planData.program
		};

		const scaledAmounts = [];

		if (planData.type === PLAN_TYPE) {
			billingPlan.frequency = planData.frequency;
			billingPlan.category = planData.category;
			billingPlan.program = planData.program;

			// Program offered on is available to all plan types not charged daily.
			if (planData.programOfferedOn && planData.programOfferedOn.length && !PLANS_CHARGED_BY_CHECKINS.includes(planData.frequency)) {
				billingPlan.programOfferedOn = planData.programOfferedOn;
			}

			if (SCALED_PLAN_FREQUENCIES.includes(planData.frequency)) {
				let maxDaysForScaledPlan = 5; // Default 5 days for scaled unless max days is different

				if (planData.requiredEnrollmentMin && !isNaN(planData.requiredEnrollmentMin)) {
					billingPlan.requiredEnrollmentMin = planData.requiredEnrollmentMin;
				}

				if (planData.requiredEnrollmentMax && !isNaN(planData.requiredEnrollmentMax)) {
					billingPlan.requiredEnrollmentMax = planData.requiredEnrollmentMax;
					maxDaysForScaledPlan = planData.requiredEnrollmentMax;
				}

				// Set the scaled amounts
				for (let i = 0; i < maxDaysForScaledPlan; i++) {
					amount = parseFloat(planData[`scaledAmount_${i}`] ?? 'a');
					try {
						check(amount, Number);
					} catch (e) {
						const dayCount = i + 1;
						throw new Meteor.Error('400', `Invalid amount for ${dayCount} Day/Wk`);
					}
					scaledAmounts.push(amount);
				}
				amount = amount.toString();
				// The billingPlan.amount should be set to the max day/wk amount
			}
			Meteor.callAsync('addBillingPlanToAllGroupsWithSelectAllInOrg', org._id, org.availableBillingPlans(), billingPlan._id)
		}
		if (planData.type === ITEM_TYPE && planData.refundableDeposit) {
			billingPlan.refundableDeposit = true;
		}
		if (planData.type == ITEM_TYPE && planData.dropInDailyRate) {
			billingPlan.dropInDailyRate = true;
		}

		if (planData.type == PUNCH_CARD_TYPE) {
			if (planData.numberOfDays < 1) {
				throw new Meteor.Error('400', 'Number of days must be more than 0')
			}
			billingPlan.numberOfDays = planData.numberOfDays;
			billingPlan.category = planData.category;
		}

		amount = parseFloat(amount);
		check(amount, Number);
		billingPlan.amount = amount;
		billingPlan.scaledAmounts = scaledAmounts;
		billingPlan.program = planData.program;
		billingPlan.offeredAtOrgIds = planData.offeredAtOrgIds;

		if (planData.expires)
			billingPlan.expires = new moment.tz(planData.expires, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();

		if (org.hasCustomization("billing/requireLedgerAccountName/enabled")) billingPlan.ledgerAccountName = planData.ledgerAccountName;
		if (org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
			billingPlan.siblingDiscountExempt = planData.siblingDiscountExempt;
			billingPlan.regFeeExempt = planData.regFeeExempt;
			billingPlan.designations = planData.designations;
			if (planData.type === PLAN_TYPE || planData.type === ITEM_TYPE) {
				const detailsInfo = {}
				if (planData.start_time && moment(planData.start_time, ["h:mm a", "HH:mm"])) {
					detailsInfo.startTime = moment(planData.start_time, ["h:mm a", "HH:mm"]).format("h:mm a");
				}
				if (planData.end_time && moment(planData.end_time, ["h:mm a", "HH:mm"])) {
					detailsInfo.endTime = moment(planData.end_time, ["h:mm a", "HH:mm"]).format("h:mm a");
				}
				if (planData.reg_start_date && moment(planData.reg_start_date, 'MM/DD/YYYY', true).isValid()) {
					detailsInfo.regStartDate = new moment.tz(planData.reg_start_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
				}
				if (planData.reg_end_date && moment(planData.reg_end_date, 'MM/DD/YYYY', true).isValid()) {
					detailsInfo.regEndDate = new moment.tz(planData.reg_end_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
				}
				if (planData.grades && planData.grades.length) {
					detailsInfo.grades = planData.grades;
				}
				if (planData.schedule_type) {
					detailsInfo.scheduleType = planData.schedule_type;
				}

				// Handle item type specifically
				if (planData.type === ITEM_TYPE) {
					switch (planData.date_type) {
						case 'dateRange':
							detailsInfo.dateType = 'dateRange';

							if (planData.service_start_date && moment(planData.service_start_date, 'MM/DD/YYYY', true).isValid()) {
								detailsInfo.serviceStartDate = new moment.tz(planData.service_start_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
							}

							if (planData.service_end_date && moment(planData.service_end_date, 'MM/DD/YYYY', true).isValid()) {
								detailsInfo.serviceEndDate = new moment.tz(planData.service_end_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
							}

							if (planData.excludeSaturdays !== undefined) {
								detailsInfo.excludeSaturdays = planData.excludeSaturdays;
							}

							if (planData.excludeSundays !== undefined) {
								detailsInfo.excludeSundays = planData.excludeSundays;
							}
							break;
						case 'recurring':
							detailsInfo.dateType = 'recurring';
							const recurringStartDateValid = planData.recurring_start_date && moment(planData.recurring_start_date, 'MM/DD/YYYY', true).isValid();
							const recurringFrequencyValid = planData.recurring_frequency && !isNaN(planData.recurring_frequency);
							const recurringOccurrencesValid = planData.recurring_occurrences && !isNaN(planData.recurring_occurrences);
							const recurringDaysValid = planData.recurring_days && planData.recurring_days.length
							if (recurringStartDateValid && recurringFrequencyValid && recurringOccurrencesValid && recurringDaysValid) {
								detailsInfo.recurringStartDate = new moment.tz(planData.recurring_start_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
								detailsInfo.recurringFrequency = parseInt(planData.recurring_frequency);
								detailsInfo.recurringOccurrences = parseInt(planData.recurring_occurrences);
								detailsInfo.recurringDays = planData.recurring_days;
							}
							break;
						case 'individualDates':
							detailsInfo.dateType = 'individualDates';
							if (planData.individual_dates && planData.individual_dates.length) {
								detailsInfo.individualDates = planData.individual_dates.map(d => new moment.tz(d, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf());
							}
							break;
					}
				}

				// Handle plan type specifically
				if (planData.type === PLAN_TYPE) {
					if (planData.date_type === 'timePeriod' && planData.time_period) {
						detailsInfo.dateType = 'timePeriod';
						detailsInfo.timePeriod = planData.time_period;
					}
					if (planData.date_type === 'timePeriodSelectiveWeeks' && planData.time_period) {
						detailsInfo.dateType = 'timePeriod';
						detailsInfo.timePeriod = planData.time_period;
						detailsInfo.selectiveWeeks = planData.selectiveWeeks;
						detailsInfo.selectiveWeekAmounts = planData.selectiveWeekAmounts;
					}
				}
				billingPlan.details = detailsInfo;
			}
		}
		let programDetails = BillingUtils.validateProgramDetails(planData.programDetails);
		if (programDetails || programDetails === '') {
			billingPlan.programDetails = programDetails;
		}
		await Orgs.updateAsync({ _id: org._id }, { $push: { "billing.plansAndItems": billingPlan } });

		Meteor.defer(Meteor.bindEnvironment(() => {
			Meteor.callAsync('trackSettingChange', {
				user: currentUser,
				org,
				dataType: 'billing.plansAndItems',
				value: { _id: billingPlan._id }
			}).then((result) => {
				Log.info("Setting change tracked successfully:", result);
			}).catch((error) => {
				Log.error("Error tracking setting change", error);
			});
		}));
	},
	async deleteBillingPlan(planId) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		await Orgs.updateAsync({ _id: (await Orgs.current())._id },
			{ $set: { "billing.plansAndItems": _.filter((await Orgs.current()).billing.plansAndItems, (p) => { return p._id != planId; }) } });

		return true;
	},
	async updateBillingPlanBundle(planData) {
		await processPermissions({
			assertions: [{ context: 'billing/configuration/plans', action: 'edit' }],
			evaluator: (person) => person.type === 'admin',
			throwError: true
		});

		const org = await Orgs.current();
		const currentUser = await Meteor.userAsync();

		let billingPlanBundle = _.find(_.deep(org, 'billing.plansAndItems'), p => p._id === planData._id);
		if (!billingPlanBundle || billingPlanBundle.type !== PLAN_BUNDLE_TYPE) {
			throw new Meteor.Error('401', 'Billing plan bundle not found.');
		}

		billingPlanBundle.plans = [];
		const plans = [];
		for (let i = 0; i < 2; i++) {
			const plan = _.find(_.deep(org, 'billing.plansAndItems'), p => p._id === planData[`plan_${i}`]);
			if (!plan) {
				throw new Meteor.Error('400', 'Invalid plan');
			}
			billingPlanBundle.plans.push(plan._id);
			plans.push(plan);
		}
		if (plans[0]._id === plans[1]._id) {
			throw new Meteor.Error('400', 'The plans cannot be the same.');
		}
		const existingBundles = _.filter(_.deep(org, 'billing.plansAndItems'), p => p._id !== planData._id && p.type === PLAN_BUNDLE_TYPE && (p.plans.includes(plans[0]._id) && p.plans.includes(plans[1]._id)));
		if (existingBundles.length) {
			throw new Meteor.Error('400', 'This bundle already exists.');
		}
		billingPlanBundle.description = plans.map(p => p.description).join(' and ');

		billingPlanBundle.scaledAmounts = [];
		let amount = '0.00';

		const plan1Max = plans[0].requiredEnrollmentMax ?? 5;
		const plan2Max = plans[1].requiredEnrollmentMax ?? 5;
		const maxDays = Math.max(plan1Max, plan2Max);

		for (let i = 0; i < maxDays; i++) {
			const amounts = [];
			for (let j = 0; j < maxDays; j++) {
				if (planData[`scaledAmount_${i}_${j}`] === undefined) {
					amount = null;
				} else {
					amount = parseFloat(planData[`scaledAmount_${i}_${j}`] ?? 'a');
				}
				try {
					if (amount !== null) {
						check(amount, Number);
					}
				} catch (e) {
					const dayCount = i + 1;
					throw new Meteor.Error('400', `Invalid amount for ${dayCount} Day/Wk`);
				}
				amounts.push(amount)
			}
			billingPlanBundle.scaledAmounts.push(amounts);
		}
		if (org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
			billingPlanBundle.regFeeExempt = planData.regFeeExempt;
			billingPlanBundle.siblingDiscountExempt = planData.siblingDiscountExempt;
			billingPlanBundle.designations = planData.designations;
		}

		let programDetails = BillingUtils.validateProgramDetails(planData.programDetails);
		if (programDetails || programDetails === '') {
			billingPlanBundle.programDetails = programDetails;
		}
		await Orgs.updateAsync(
			{ _id: org._id, 'billing.plansAndItems._id': planData._id },
			{ $set: { 'billing.plansAndItems.$': billingPlanBundle } }
		);

		Meteor.defer(Meteor.bindEnvironment(() => {
			Meteor.callAsync('trackSettingChange', {
				user: currentUser,
				org,
				dataType: 'billing.plansAndItems',
				value: { _id: billingPlanBundle._id }
			}).then((result) => {
				Log.info("Setting change tracked successfully:", result);
			}).catch((error) => {
				Log.error("Error tracking setting change", error);
			});
		}));
	},
	async updateBillingPlan(planData) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const org = await Orgs.current();
		const currentUser = await Meteor.userAsync();

		if (org.hasCustomization(AvailableCustomizations.REQUIRE_LEDGER_ACCOUNT) && !planData.ledgerAccountName) {
			throw new Meteor.Error(500, "A ledger account must be assigned before this plan can be saved");
		}

		let billingPlan = _.find(_.deep(org, "billing.plansAndItems"), p => p._id == planData._id);

		if (!billingPlan) {
			throw new Meteor.Error(500, "Billing plan not found.");
		}

		if (planData.type === PUNCH_CARD_TYPE) {
			if (planData.numberOfDays < 1) {
				throw new Meteor.Error('400', 'Number of days must be more than 0')
			}
			billingPlan.numberOfDays = planData.numberOfDays;
		}

		let amount = planData.amount;
		const scaledAmounts = [];

		if (planData.type === PLAN_TYPE) {
			// Program offered on is available to any plan type except for the daily charged ones.
			if (planData.programOfferedOn && planData.programOfferedOn.length && !PLANS_CHARGED_BY_CHECKINS.includes(planData.frequency)) {
				billingPlan.programOfferedOn = planData.programOfferedOn;
			} else {
				delete billingPlan.programOfferedOn;
			}

			if (SCALED_PLAN_FREQUENCIES.includes(planData.frequency)) {
				let maxDaysForScaledPlan = 5; // Default 5 days for scaled unless max days is different


				if (planData.requiredEnrollmentMin && !isNaN(planData.requiredEnrollmentMin)) {
					billingPlan.requiredEnrollmentMin = planData.requiredEnrollmentMin;
				} else if (billingPlan.requiredEnrollmentMin) {
					delete billingPlan.requiredEnrollmentMin;
				}

				if (planData.requiredEnrollmentMax && !isNaN(planData.requiredEnrollmentMax)) {
					billingPlan.requiredEnrollmentMax = planData.requiredEnrollmentMax;
					maxDaysForScaledPlan = planData.requiredEnrollmentMax;
				} else if (billingPlan.requiredEnrollmentMax) {
					delete billingPlan.requiredEnrollmentMax;
				}

				// Set the scaled amounts
				for (let i = 0; i < maxDaysForScaledPlan; i++) {
					amount = parseFloat(planData[`scaledAmount_${i}`] ?? 'a');
					try {
						check(amount, Number);
					} catch (e) {
						const dayCount = i + 1;
						throw new Meteor.Error('400', `Invalid amount for ${dayCount} Day/Wk`);
					}
					scaledAmounts.push(amount);
				}
				amount = amount.toString();
				// The billingPlan.amount should be set to the max day/wk amount
			} else {
				// Delete scaled specific properties if they exist and not a scaled plan
				delete billingPlan.requiredEnrollmentMin;
				delete billingPlan.requiredEnrollmentMax;
			}

		} else {
			// Just in case someone changes the plan type from a plan to a non-plan
			delete billingPlan.programOfferedOn;
			delete billingPlan.requiredEnrollmentMin;
			delete billingPlan.requiredEnrollmentMax;
		}

		amount = parseFloat(amount);
		check(amount, Number);

		billingPlan.description = planData.description;
		billingPlan.amount = amount;
		billingPlan.scaledAmounts = scaledAmounts;
		billingPlan.type = planData.type;
		billingPlan.program = planData.program;
		billingPlan.numberOfDays = planData.numberOfDays;
		billingPlan.offeredAtOrgIds = planData.offeredAtOrgIds;

		if (org.hasCustomization(AvailableCustomizations.REQUIRE_LEDGER_ACCOUNT)) {
			billingPlan.ledgerAccountName = planData.ledgerAccountName;
		}

		if (planData.type === PLAN_TYPE && planData.frequency) {
			billingPlan.frequency = planData.frequency;
		} else {
			delete billingPlan.frequency;
		}

		if (planData.type === PLAN_TYPE && planData.category) {
			billingPlan.category = planData.category;
		} else {
			delete billingPlan.category;
		}

		if (planData.expires) {
			billingPlan.expires = new moment.tz(planData.expires, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
		} else {
			delete billingPlan.expires;
		}

		if (planData.refundableDeposit) {
			billingPlan.refundableDeposit = true;
		} else {
			delete billingPlan.refundableDeposit;
		}

		if (planData.dropInDailyRate) {
			billingPlan.dropInDailyRate = true;
		} else {
			delete billingPlan.dropInDailyRate;
		}

		if (org.hasCustomization(AvailableCustomizations.REGISTRATION_FLOW)) {
			billingPlan.regFeeExempt = planData.regFeeExempt;
			billingPlan.siblingDiscountExempt = planData.siblingDiscountExempt;
			billingPlan.designations = planData.designations;
			if (planData.type === PLAN_TYPE || planData.type === ITEM_TYPE) {
				const detailsInfo = {}
				if (planData.start_time && moment(planData.start_time, ["h:mm a", "HH:mm"])) {
					detailsInfo.startTime = moment(planData.start_time, ["h:mm a", "HH:mm"]).format("h:mm a");
				}
				else {
					delete billingPlan.details?.startTime;
				}

				if (planData.end_time && moment(planData.end_time, ["h:mm a", "HH:mm"])) {
					detailsInfo.endTime = moment(planData.end_time, ["h:mm a", "HH:mm"]).format("h:mm a");
				}
				else {
					delete billingPlan.details?.endTime;
				}

				if (planData.reg_start_date && moment(planData.reg_start_date, 'MM/DD/YYYY', true).isValid()) {
					detailsInfo.regStartDate = new moment.tz(planData.reg_start_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
				}
				else {
					delete billingPlan.details?.regStartDate;
				}

				if (planData.reg_end_date && moment(planData.reg_end_date, 'MM/DD/YYYY', true).isValid()) {
					detailsInfo.regEndDate = new moment.tz(planData.reg_end_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
				}
				else {
					delete billingPlan.details?.regEndDate;
				}

				if (planData.grades && planData.grades.length) {
					detailsInfo.grades = planData.grades;
				}
				else {
					delete billingPlan.details?.grades;
				}

				if (planData.schedule_type) {
					detailsInfo.scheduleType = planData.schedule_type;
				}
				else {
					delete billingPlan.details?.scheduleType;
				}


				// Handle item type specifically
				if (planData.type === ITEM_TYPE) {
					delete billingPlan.details?.timePeriod;
					switch (planData.date_type) {
						case 'dateRange':
							detailsInfo.dateType = 'dateRange';
							if (planData.service_start_date && moment(planData.service_start_date, 'MM/DD/YYYY', true).isValid()) {
								detailsInfo.serviceStartDate = new moment.tz(planData.service_start_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
							}
							if (planData.service_end_date && moment(planData.service_end_date, 'MM/DD/YYYY', true).isValid()) {
								detailsInfo.serviceEndDate = new moment.tz(planData.service_end_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
							}

							if (planData.excludeSaturdays !== undefined) {
								detailsInfo.excludeSaturdays = planData.excludeSaturdays;
							} else if (billingPlan.details?.excludeSaturdays !== undefined) {
								delete billingPlan.details?.excludeSaturdays;
							}

							if (planData.excludeSundays !== undefined) {
								detailsInfo.excludeSundays = planData.excludeSundays;
							} else if (billingPlan.details?.excludeSundays !== undefined) {
								delete billingPlan.details?.excludeSundays;
							}

							delete billingPlan.details?.recurringStartDate;
							delete billingPlan.details?.recurringFrequency;
							delete billingPlan.details?.recurringOccurrences;
							delete billingPlan.details?.recurringDays;
							delete billingPlan.details?.individualDates;
							break;
						case 'recurring':
							detailsInfo.dateType = 'recurring';
							const recurringStartDateValid = planData.recurring_start_date && moment(planData.recurring_start_date, 'MM/DD/YYYY', true).isValid();
							const recurringFrequencyValid = planData.recurring_frequency && !isNaN(planData.recurring_frequency);
							const recurringOccurrencesValid = planData.recurring_occurrences && !isNaN(planData.recurring_occurrences);
							const recurringDaysValid = planData.recurring_days && planData.recurring_days.length
							if (recurringStartDateValid && recurringFrequencyValid && recurringOccurrencesValid && recurringDaysValid) {
								detailsInfo.recurringStartDate = new moment.tz(planData.recurring_start_date, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf();
								detailsInfo.recurringFrequency = parseInt(planData.recurring_frequency);
								detailsInfo.recurringOccurrences = parseInt(planData.recurring_occurrences);
								detailsInfo.recurringDays = planData.recurring_days;
							}
							delete billingPlan.details?.serviceStartDate;
							delete billingPlan.details?.serviceEndDate;
							delete billingPlan.details?.individualDates;
							delete billingPlan.details?.excludeSaturdays;
							delete billingPlan.details?.excludeSundays;
							break;
						case 'individualDates':
							detailsInfo.dateType = 'individualDates';
							if (planData.individual_dates && planData.individual_dates.length) {
								detailsInfo.individualDates = planData.individual_dates.map(d => new moment.tz(d, "MM/DD/YYYY", org.getTimezone()).startOf("day").valueOf());
							}
							delete billingPlan.details?.serviceStartDate;
							delete billingPlan.details?.serviceEndDate;
							delete billingPlan.details?.recurringStartDate;
							delete billingPlan.details?.recurringFrequency;
							delete billingPlan.details?.recurringOccurrences;
							delete billingPlan.details?.recurringDays;
							delete billingPlan.details?.excludeSaturdays;
							delete billingPlan.details?.excludeSundays;
							break;
						default:
							delete billingPlan.details?.serviceStartDate;
							delete billingPlan.details?.serviceEndDate;
							delete billingPlan.details?.recurringStartDate;
							delete billingPlan.details?.recurringFrequency;
							delete billingPlan.details?.recurringOccurrences;
							delete billingPlan.details?.recurringDays;
							delete billingPlan.details?.indivdualDates;
							delete billingPlan.details?.dateType;
							delete billingPlan.details?.excludeSaturdays;
							delete billingPlan.details?.excludeSundays;
							break;
					}
				}

				if (planData.type === PLAN_TYPE) {
					delete billingPlan.details?.serviceStartDate;
					delete billingPlan.details?.serviceEndDate;
					delete billingPlan.details?.recurringStartDate;
					delete billingPlan.details?.recurringFrequency;
					delete billingPlan.details?.recurringOccurrences;
					delete billingPlan.details?.recurringDays;
					delete billingPlan.details?.indivdualDates;
					delete billingPlan.details?.selectiveWeeks;
					delete billingPlan.details?.excludeSaturdays;
					delete billingPlan.details?.excludeSundays;
					if (planData.date_type === 'timePeriod' && planData.time_period) {
						detailsInfo.dateType = 'timePeriod';
						detailsInfo.timePeriod = planData.time_period;
					} else if (planData.date_type === 'timePeriodSelectiveWeeks' && planData.time_period) {
						detailsInfo.dateType = 'timePeriod';
						detailsInfo.timePeriod = planData.time_period;
						detailsInfo.selectiveWeeks = planData.selectiveWeeks;
						detailsInfo.selectiveWeekAmounts = planData.selectiveWeekAmounts;
					} else {
						delete billingPlan.details?.dateType;
						delete billingPlan.details?.timePeriod;
					}
				}
				billingPlan.details = detailsInfo;
			}
		} else {
			delete billingPlan.details;
		}

		let programDetails = BillingUtils.validateProgramDetails(planData.programDetails);
		if (programDetails || programDetails === '') {
			billingPlan.programDetails = programDetails;
		}
		await Orgs.updateAsync(
			{ _id: org._id, "billing.plansAndItems._id": planData._id },
			{ $set: { "billing.plansAndItems.$": billingPlan } }
		);

		Meteor.defer(Meteor.bindEnvironment(() => {
			Meteor.callAsync('trackSettingChange', {
				user: currentUser,
				org,
				dataType: 'billing.plansAndItems',
				value: { _id: billingPlan._id }
			}).then((result) => {
				Log.info("Setting change tracked successfully:", result);
			}).catch((error) => {
				Log.error("Error tracking setting change", error);
			});
		}));

		// Update bundles with the plan to change the description
		const bundles = _.filter(_.deep(org, 'billing.plansAndItems'), p => p.type === PLAN_BUNDLE_TYPE && p.plans.includes(planData._id));
		for (const bundle of bundles) {
			const plans = [];
			for (let i = 0; i < 2; i++) {
				const plan = _.find(_.deep(org, 'billing.plansAndItems'), p => p._id === bundle.plans[i]);
				if (!plan) {
					continue;
				}
				plans.push(plan);
			}
			await Orgs.updateAsync(
				{ _id: org._id, 'billing.plansAndItems._id': bundle._id },
				{ $set: { 'billing.plansAndItems.$.description': plans.map(p => p.description).join(' and ') } }
			);

			Meteor.defer(Meteor.bindEnvironment(() => {
				Meteor.callAsync('trackSettingChange', {
					user: currentUser,
					org,
					dataType: 'billing.plansAndItems',
					value: { _id: bundle._id }
				}).then((result) => {
					Log.info("Setting change tracked successfully:", result);
				}).catch((error) => {
					Log.error("Error tracking setting change", error);
				});
			}));
		}
	},
	async suspendBillingPlan(planId, suspendUntil) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		let currentPlans = (await Orgs.current()).billing.plansAndItems;
		let currentPlan = _.find(currentPlans, (p) => { return p._id == planId; });
		if (currentPlan) {
			currentPlan.suspendUntil = suspendUntil ? new moment(suspendUntil, "MM/DD/YYYY").startOf("day").valueOf() : null;
			await Orgs.updateAsync({ _id: (await Orgs.current())._id },
				{ $set: { "billing.plansAndItems": currentPlans } }
			);
		}
	},
	async archiveBillingPlan(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/plans", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		let currentPlans = (await Orgs.current()).billing.plansAndItems;
		let currentPlan = _.find(currentPlans, (p) => { return p._id == options.planId; });
		if (currentPlan) {
			if (options.archive)
				currentPlan.archived = true;
			else
				delete currentPlan.archived;
			await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { $set: { 'billing.plansAndItems': currentPlans } });
			if (options.unenroll) {
				let query = null;
				if (currentPlan.type !== PLAN_BUNDLE_TYPE) {
					query = { '$pull': { 'billing.enrolledPlans': { 'planDetails._id': options.planId } } };
					await People.updateAsync({ orgId: (await Orgs.current())._id }, query, { multi: true });
				} else {
					query = { $unset: { 'billing.enrolledPlans.$.bundlePlanId': 1 } };
					await People.updateAsync({ orgId: (await Orgs.current())._id, 'billing.enrolledPlans.bundlePlanId': currentPlan._id }, query, { multi: true });
				}
			}

		}
	},
	'insertCouponCode': async function (couponData) {
		await insertCoupon(couponData, Orgs);
	},
	'updateCouponCode': async function (couponData) {
		await updateCoupon(couponData, Orgs);
	},
	async deleteCouponCode(couponId) {
		await deleteCoupon(couponId, Orgs);
		return true;
	},
	async suspendBillingCoupon(couponId, suspendUntil) {
		await suspendCoupon(couponId, Orgs, suspendUntil);
	},
	async archiveBillingCoupon(options) {
		await archiveCoupon(options, Orgs);
	},
	'updateCurrentNumberOfRegistrations': async function ({ org, currentCoupon }) {
		await Orgs.updateAsync(
			{ _id: org._id, "billing.couponCodes._id": currentCoupon._id },
			{ $set: { "billing.couponCodes.$": currentCoupon } }
		);
	},
	"addSingleInstallmentCouponUsedBy": async function ({ coupon, update }) {
		await Orgs.updateAsync({ _id: (await Orgs.current())._id, "billing.couponCodes.code": coupon.code }, { $push: { "billing.couponCodes.$.singleInstallmentUsedBy": update } });
	},
	"removeSingleInstallmentCouponUsedBy": async function ({ childId, planId, code }) {
		await Orgs.updateAsync({ _id: (await Orgs.current())._id, "billing.couponCodes.code": code }, { $pull: { "billing.couponCodes.$.singleInstallmentUsedBy": { childId: childId, planId: planId } } });
	},
	async disableBilling() {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { $set: { "billing.enabled": false } });
		return true;
	},
	async updateBillingSettings(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type === "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		let updateData = {};
		if (options.generate_when) updateData["billing.scheduling.generateWhen"] = options.generate_when;
		if (options.generate_day) {
			updateData["billing.scheduling.generateDay"] = options.generate_day;
			updateData["billing.scheduling.weeklyPlanDueDay"] = options.weekly_plan_due === "fixed" ? options.weekly_plan_due_day : null;
		}
		if (options.generate_monthday) {
			updateData["billing.scheduling.generateMonthDay"] = options.generate_monthday;
			updateData["billing.scheduling.monthlyPlanDueDay"] = options.monthly_plan_due === "fixed" ? options.monthly_plan_due_day : null;
		}
		if (options.generate_biweeklydate) updateData["billing.scheduling.generateBiWeeklyDate"] = options.generate_biweeklydate;
		if (options.late_fee) updateData["billing.scheduling.lateFee"] = options.late_fee;
		if (options.grace_period_days) updateData["billing.scheduling.gracePeriodDays"] = options.grace_period_days;
		if (options.discount_calculation) updateData["billing.toplinePercentDiscounts"] = options.discount_calculation === "topline";
		if (options.allow_deposit_report_edit) updateData["billing.allowDepositReportEdit"] = options.allow_deposit_report_edit === "true";
		if (options.allow_payments_before_due_date) {
			updateData["billing.scheduling.disablePaymentsBeforeDueDate"] = !(options.allow_payments_before_due_date === "yes");
		}
		if (options.missed_invoice_interval) {
			updateData["billing.scheduling.missedInvoiceInterval"] = options.missed_invoice_interval;
		}

		if ((await Orgs.current()).hasCustomization("registrationFlow")) {
			if (options.drop_in_buffer && options.drop_in_buffer !== "") {
				updateData["billing.scheduling.dropInBuffer"] = options.drop_in_buffer;
			}
		}

		updateData["billing.scheduling.assessLateFee"] = options.use_late_fee;
		updateData["billing.scheduling.assessLateFeeItemId"] = options.use_late_fee ? options.late_fee_item : null;
		updateData["billing.scheduling.assessLateFeeDays"] = options.use_late_fee ? parseInt(options.late_fee_days) : null;

		if (options.registration_discount_types) {
			updateData["familyRegistrationSettings.discountsAllowed"] = options.registration_discount_types;
		}

		if (_.keys(updateData).length > 0) await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: updateData });
	},
	async enrollBillingPlan(options, orgId = null, fromRegistration = false) {
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/planAssignments", action: "edit" }],
				evaluator: (person) => person.type === "admin",
				throwError: true
			});
		}

		const currentUser = fromRegistration ? null : await Meteor.userAsync();
		const org = await Orgs.findOneAsync({ _id: orgId || currentUser.orgId });
		const person = await People.findOneAsync({ orgId: org._id, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot associate billing plan with this type of person");

		const currentPlans = _.deep(person, "billing.enrolledPlans");

		if (org.hasCustomization("billing/plans/allowSingleDiscount"))
			await checkDiscountCounts(options.allocations, org);

		const timezone = org.getTimezone();
		// New enrolled plan is using _id as the _id in org.billing.plansAndItems
		const selectedPlan = _.find(org.billing.plansAndItems, (p) => { return p._id == options.plan }),
			newPlan = {
				_id: options.plan,
				planDetails: selectedPlan,
				enrollmentDate: new moment.tz(options.enrollment_date, "MM/DD/YYYY", timezone).valueOf(),
				allocations: options.allocations,
				createdAt: new moment.tz(timezone).valueOf()
			};
		MiscUtils.setDocumentId(newPlan, 'uid');
		if (options.expiration_date) newPlan.expirationDate = new moment.tz(options.expiration_date, "MM/DD/YYYY", timezone).valueOf();
		if (options.override_rate) newPlan.overrideRate = roundToTwo(parseFloat(options.override_rate));
		if (options.enrollment_forecast_start) newPlan.enrollmentForecastStartDate = new moment.tz(options.enrollment_forecast_start, "MM/DD/YYYY", timezone).valueOf();
		if (options.enrollment_forecast_end) newPlan.enrollmentForecastEndDate = new moment.tz(options.enrollment_forecast_end, "MM/DD/YYYY", timezone).valueOf();

		if (org.hasCustomization(AvailableCustomizations.COUPON_CODES)) {
			const matchingCoupons = []
			if (options?.allocations && options?.allocations.length > 0) {
				options.allocations.forEach((allocation) => {
					org.billing.couponCodes.forEach((coupon) => {
						if (allocation.discountType === coupon.code) {
							matchingCoupons.push(coupon)
						}
					})
				})
			}

			for (const coupon of matchingCoupons) {
				if (coupon?.isSingleInstallmentCoupon) {
					const familyRelationships = await Relationships.find({
						orgId: org._id,
						targetId: options.personId,
						relationshipType: "family"
					}).mapAsync(rel => rel.personId);

					const update = {
						childId: options.personId,
						familyId: [...new Set(familyRelationships)] || [],
						planId: selectedPlan._id,
						couponCode: coupon.code
					}
					await Orgs.updateAsync({ _id: (await Orgs.current())._id, "billing.couponCodes.code": coupon.code }, { $push: { "billing.couponCodes.$.singleInstallmentUsedBy": update } });
				}
				await Orgs.updateAsync({ _id: (await Orgs.current())._id, "billing.couponCodes.code": coupon.code }, { $inc: { "billing.couponCodes.$.numberOfRegistrations": 1 } });
			}
		}

		await People.updateAsync({ _id: person._id }, { $push: { "billing.enrolledPlans": newPlan } });
		await Orgs.updateAsync({ _id: org._id }, { $inc: { "billing.stats.totalEnrollmentCount": 1 } });

	},
	async modifyBillingPlan(options, fromRegistration = false) {
		if (!fromRegistration) {
			await processPermissions({
				assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/planAssignments", action: "edit" }],
				evaluator: (person) => person.type == "admin",
				throwError: true
			});
		}

		const currentUser = await Meteor.userAsync(), person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot associate billing plan with this type of person");

		const currentPlans = _.deep(person, "billing.enrolledPlans");
		const currentPlan = _.find(currentPlans, (p) => { return p._id === options.existingPlanId && p.createdAt === options.createdAt });
		if (!currentPlan)
			throw new Meteor.Error(500, "Couldn't match existing plan enrollment for that customer");

		if ((await Orgs.current()).hasCustomization("billing/plans/allowSingleDiscount"))
			await checkDiscountCounts(options.allocations);

		const timezone = (await Orgs.current()).getTimezone();

		const updateDoc = {
			"billing.enrolledPlans.$.enrollmentForecastStartDate": options.enrollment_forecast_start ? new moment.tz(options.enrollment_forecast_start, "MM/DD/YYYY", timezone).valueOf() : null,
			"billing.enrolledPlans.$.enrollmentForecastEndDate": options.enrollment_forecast_end ? new moment.tz(options.enrollment_forecast_end, "MM/DD/YYYY", timezone).valueOf() : null,
			"billing.enrolledPlans.$.allocations": options.allocations,
			"billing.enrolledPlans.$.updatedAt": new moment.tz(timezone).valueOf(),
			"billing.enrolledPlans.$.updatedBy": currentUser._id,
		};
		updateDoc["billing.enrolledPlans.$.overrideRate"] = options.override_rate ? roundToTwo(parseFloat(options.override_rate)) : null;
		if (!currentPlan.reservationId) {
			updateDoc["billing.enrolledPlans.$.enrollmentDate"] = new moment.tz(options.enrollment_date, "MM/DD/YYYY", timezone).valueOf();
			updateDoc["billing.enrolledPlans.$.expirationDate"] = options.expiration_date ? new moment.tz(options.expiration_date, "MM/DD/YYYY", timezone).valueOf() : null;
		}
		await People.updateAsync({ _id: person._id, "billing.enrolledPlans._id": options.existingPlanId, "billing.enrolledPlans.createdAt": options.createdAt }, { $set: updateDoc });
	},
	async removeBillingPlan(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/planAssignments", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(), person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot find matching person");
		const allocations = person.billing.enrolledPlans.find((plan) => plan._id === options.planId)?.allocations
		const currentOrg = await Orgs.findOneAsync({ _id: currentUser.orgId });
		const orgCouponCodes = currentOrg.billing?.couponCodes || []
		if (allocations !== undefined) {
			for (let allocation of allocations) {

				if (allocation.allocationType === 'coupon') {
					const matchingCoupon = orgCouponCodes.find((coupon) => {
						return coupon.code === allocation.discountType
					})
					if (matchingCoupon?.isSingleInstallmentCoupon) {
						await Orgs.updateAsync({
							_id: (await Orgs.current())._id,
							"billing.couponCodes.code": allocation.discountType
						}, {
							$pull: {
								"billing.couponCodes.$.singleInstallmentUsedBy": {
									childId: options.personId,
									planId: options.planId
								}
							}
						});
					}
					await Orgs.updateAsync({
						_id: (await Orgs.current())._id,
						"billing.couponCodes.code": allocation.discountType
					}, { $inc: { "billing.couponCodes.$.numberOfRegistrations": -1 } });
				}
			}
		}

		return await People.updateAsync({ _id: person._id }, { $pull: { "billing.enrolledPlans": { _id: options.planId, createdAt: options.createdAt } } });
	},
	async unlinkBillingPlan(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/planAssignments", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		let person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot find matching person");

		await People.updateAsync({ _id: person._id, "billing.enrolledPlans._id": options.planId, "billing.enrolledPlans.createdAt": options.createdAt }, { $unset: { "billing.enrolledPlans.$.reservationId": 1 } });
		person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		const plan = _.find(_.deep(person, 'billing.enrolledPlans'), p => p._id === options.planId && p.createdAt === options.createdAt);
		if (plan && plan.bundlePlanId) {
			BillingUtils.recalculateBundleDiscount(person, plan.bundlePlanId);
		}
	},
	async insertBillingCharge(options, orgId = null, fromRegistration = false, allReservationIds=[]) {
		try {
			if (!fromRegistration) {
				await processPermissions({
					assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/itemCharges", action: "edit" }],
					evaluator: (person) => person.type == "admin",
					throwError: true
				});
			}
			const { currentUser, currentOrg, person, group } = await InsertBillingChargeService.getCurrentUserOrgPersonGroup(orgId, options)
			const { currentItem, price } = InsertBillingChargeService.validateChargeOptions(options, person, group, currentOrg);
			const pushData = InsertBillingChargeService.getPushData(options, currentItem, currentOrg, price);
			const peopleIds = await InsertBillingChargeService.createPeopleIds(person, group, options, currentOrg);

			if(pushData['billing.enrolledItems'] && allReservationIds.length){
				const newlyCreatedReservations = await Reservations.find({ _id: { "$in": allReservationIds }, orgId: currentOrg._id, selectedPerson: options.personId }).fetchAsync();
				const matchedReservations = newlyCreatedReservations?.filter(r => r.generatedFromBillingCharge === pushData['billing.enrolledItems'].originalItem._id);

				for (let matchedReservation of matchedReservations) {
					await Reservations.updateAsync(matchedReservation._id, { $set: { enrolledItemId: pushData['billing.enrolledItems']._id } });
				}
			}

			await People.updateAsync({ _id: { $in: peopleIds } }, { $push: pushData }, { multi: true });
			if (options.chargebackReference) {
				if (options.markResolved) {
					Meteor.callAsync("markChargebackResolved", {
						invoiceId: options.chargebackReference.invoiceId,
						chargebackPspId: options.chargebackReference.chargebackPspId
					}).then((result) => {
						Log.info("Chargeback marked as resolved successfully:", result);
					}).catch((error) => {
						Log.error("Error marking chargeback as resolved", error);
					});
				}
				if (options.invoiceNow) {
					Meteor.callAsync("sendManualPlanInvoice", {
						personId: options.chargebackReference.personId,
						allowItemsOnly: true
					}).then((result) => {
						Log.info("Manual plan invoice sent successfully:", result);
					}).catch((error) => {
						Log.error("Error sending manual plan invoice", error);
					});
				}
			}
		} catch (error) {
			Log.error("Error in 'insertBillingCharge':" + error);
			throw new Meteor.Error(error.error, error.reason);
		}
	},
	async removeBillingCharge(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/itemCharges", action: "edit" },],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(), person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type !== 'person') {
			throw new Meteor.Error('500', 'Cannot find matching person');
		}

		const updateData = {
			$pull: { 'billing.pendingCharges': { _id: options.chargeId } },
		}
		const charge = person.billing?.pendingCharges?.find(pc => pc._id === options.chargeId);
		if (charge?.originalItem) {
			const item = charge.originalItem;
			if (item.type === ITEM_TYPE && item.details?.dateType) {
				let found = false;
				const enrolledItems = (person.billing?.enrolledItems ?? []).filter(ei => {
					if (!found && ei.originalItem._id === item._id) {
						found = true;
						return false;
					}
					return true;
				});
				updateData['$set'] = {};
				updateData['$set']['billing.enrolledItems'] = enrolledItems;
			} else if (item.type === PUNCH_CARD_TYPE) {
				let found = false;
				const enrolledPunchCards = (person.billing?.enrolledPunchCards ?? []).filter(epc => {
					if (!found && epc.originalItem._id === item._id) {
						found = true;
						return false;
					}
					return true;
				});
				updateData['$set'] = {};
				updateData['$set']['billing.enrolledPunchCards'] = enrolledPunchCards;
			}
		}

		return await People.updateAsync({ _id: person._id }, updateData);
	},
	async updateInvoiceNotes(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync();
		const invoice = await Invoices.findOneAsync({ orgId: currentUser.orgId, _id: options.invoiceId });

		if (!invoice) {
			throw new Meteor.Error(500, "Cannot associate billing notes with this invoice");
		}

		await Invoices.updateByIdWithJournalEntry(options.invoiceId, { "$set": { "invoiceNotes": options.notes } }, {
			userId: currentUser._id,
			personId: currentUser.personId,
			orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
			reason: `Updated invoice notes`,
			reasonLocation: 'lib/methods.js:updateInvoiceNotes',
		});
	},
	async updateBillingNotes(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }, { context: "billing/invoices/billingNotes", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(), person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot associate billing notes with this type of person");
		await People.updateAsync({ _id: options.personId }, { "$set": { "billing.billingNotes": options.notes } });
	},
	async updateBillingFamilySplit(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(), person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot edit family splits for this type of person");

		await People.updateAsync({ _id: person._id }, { $set: { "billing.familySplits": options.allocations } });
	},
	async removeBillingFamilySplit(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(), person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot remove family splits for this type of person");

		await People.updateAsync({ _id: person._id }, { $unset: { "billing.familySplits": 1 } });
	},
	async configureAutoPay(options, orgId = null, fromRegistration = false) {
		let currentUser = orgId ? null : await Meteor.userAsync();
		let historyPerformedByName = null;

		if (!fromRegistration) {
			const currentPerson = await currentUser?.fetchPerson();

			const isAdmin = currentPerson?.type === "admin";
			const isCurrentUser = currentPerson?._id === options.personId;

			if (!currentUser || !(isAdmin || isCurrentUser)) {
				throw new Meteor.Error(403, "Access denied");
			}
		} else if (orgId && fromRegistration) {
			historyPerformedByName = HistoryAuditPeoplePerformedByNames.PLR;
		}

		const person = await People.findOneAsync({ orgId: orgId || currentUser.orgId, _id: options.personId });

		if (!person) {
			throw new Meteor.Error(500, "Cannot find matching person");
		}

		const personConnectedBankAcc = await person.connectedBankAccount();
		if (options.account_type === "bank_account" && !(personConnectedBankAcc && personConnectedBankAcc.status === "verified")) {
			throw new Meteor.Error(500, "Currently there is no valid, verified bank account associated with this account");
		}

		if (options.account_type === "card" && !await person.connectedCreditCard()) {
			throw new Meteor.Error(500, "Currently there is no valid credit card associated with this account");
		}

		let query = {
			"billing.autoPay": options.account_type,
			"billing.autoPayAmount": null,
			"billing.autoPayUpdatedAt": new moment().valueOf()
		};

		if (options.payment_amount_type === "specific") {
			if (options.specific_amount <= 0) {
				throw new Meteor.Error(500, "Scheduled payment amount must be greater than zero");
			}

			query["billing.autoPayAmount"] = options.specific_amount;
		}

		const result = await People.updateAsync({ _id: person._id }, { $set: query, $unset: { "billing.autoPayRemovedAt": 1 } });

		if (Meteor.isServer) {
			// Log the autopay enrollment
			const currentState = await People.findOneAsync({ _id: person._id });
			const paymentTypePretty = options.account_type === "card" ? "Credit Card" : "Bank Account";
			const lastFour = BillingUtils.getLastFourByPaymentType(person, options.account_type);
			let details = `Autopay enrolled for ${paymentTypePretty} ending in ${lastFour}`;
			if (options.payment_amount_type === "specific" && options.specific_amount > 0) {
				details += ` with a maximum daily amount of $${options.specific_amount}`;
			}

			const historyOptions = {
				callbackString: HistoryAuditRecordTypes.PAYMENT_METHOD,
				changeType: HistoryAuditChangeTypes.EDIT,
				details,
				personId: person._id,
				orgId: person.orgId,
				currentState,
				previousState: { ...person },
				performedByUser: currentUser,
				performedByName: historyPerformedByName,
			};

			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for person insert", error);
			});
		}

		return result;
	},

	async removeAutoPay(options) {
		const currentUser = await Meteor.userAsync();

		if (!currentUser) {
			throw new Meteor.Error(403, "Access denied");
		}

		const currentPerson = await currentUser?.fetchPerson();
		const isAdmin = currentPerson?.type === "admin";
		const isCurrentUser = currentPerson?._id === options.personId;

		if (!(isAdmin || isCurrentUser)) {
			throw new Meteor.Error(403, "Access denied");
		}

		const person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });

		if (!person) {
			throw new Meteor.Error(500, "Cannot find matching person");
		}

		const autoPayType = person.billing?.autoPay;

		if (!autoPayType) {
			throw new Meteor.Error(500, "Person does not have auto pay configured");
		}

		const result = await People.updateAsync({ _id: person._id }, { $unset: { "billing.autoPay": 1 }, $set: { "billing.autoPayRemovedAt": new moment().valueOf() } });

		if (Meteor.isServer) {
			// Log the autopay removal
			const currentState = await People.findOneAsync({ _id: person._id });
			const paymentTypePretty = autoPayType === "card" ? "Credit Card" : "Bank Account";
			const lastFour = BillingUtils.getLastFourByPaymentType(person, autoPayType);

			const historyOptions = {
				callbackString: HistoryAuditRecordTypes.PAYMENT_METHOD,
				changeType: HistoryAuditChangeTypes.EDIT,
				details: `Autopay removed for ${paymentTypePretty} ending in ${lastFour}`,
				personId: person._id,
				orgId: person.orgId,
				previousState: { ...person },
				currentState,
				performedByUser: currentUser,
			};

			Meteor.callAsync('logHistory', historyOptions).then((result) => {
				Log.info("History record created successfully:", result);
			}).catch((error) => {
				Log.info("Error creating history record for person insert", error);
			});
		}

		return result;
	},

	async voidCreditMemo(options) {

		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(),
			person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId }),
			currentOrg = await Orgs.current();

		if (!person) throw new Meteor.Error(500, "Cannot find matching person");

		const matchedCreditMemo = _.chain(person)
			.deep("billing.creditMemos")
			.find(cm => cm._id == options.creditMemoId)
			.value();

		if (!matchedCreditMemo) throw new Meteor.Error(500, "Cannot find specified credit memo.");

		const setQuery = {
			"billing.creditMemos.$.openAmount": 0,
			"billing.creditMemos.$.voidedBy": currentUser._id,
			"billing.creditMemos.$.voidedByPersonId": person._id,
			"billing.creditMemos.$.voidedAt": new moment().valueOf(),
			"billing.creditMemos.$.voidedNotes": options.notes
		};
		if (options.manuallyResolvedSecurityDeposit) {
			setQuery["billing.creditMemos.$.manuallyResolvedAt"] = new Date().valueOf();
			setQuery["billing.creditMemos.$.manuallyResolvedByPersonId"] = currentUser.personId;
			setQuery["billing.creditMemos.$.voidedAmount"] = parseFloat(matchedCreditMemo.openAmount);
		} else {
			const appliedInvoices = Invoices.find({ orgId: currentUser.orgId, "credits.creditMemoId": matchedCreditMemo._id });
			await appliedInvoices.forEachAsync(invoice => {

				let totalAmount = 0;
				for (let creditIndex = 0; creditIndex < invoice.credits.length; creditIndex++) {
					const setInvoiceQuery = {};
					const credit = invoice.credits[creditIndex];
					if (credit?.creditMemoId == matchedCreditMemo._id) {
						setInvoiceQuery[`credits.${creditIndex}.amount`] = 0;
						setInvoiceQuery[`credits.${creditIndex}.voidedAt`] = new moment().valueOf();
						setInvoiceQuery[`credits.${creditIndex}.voidedBy`] = currentUser._id;
						setInvoiceQuery[`credits.${creditIndex}.voidedByPersonId`] = person._id;
						setInvoiceQuery[`credits.${creditIndex}.voidedAmount`] = credit.amount;
						setInvoiceQuery[`credits.${creditIndex}.voidedNotes`] = options.notes;
						totalAmount = totalAmount + credit.amount;

						if (credit.amount > 0) {
							const updateInvoiceQuery = {};

							updateInvoiceQuery["$set"] = setInvoiceQuery;
							if (credit?.type == "reimbursement" && credit?.creditMemoType?.startsWith("prepaid_")) {
							} else {
								updateInvoiceQuery["$inc"] = { "openAmount": credit.amount };
							}
							// Not sure if we should be firing an Invoice.update command in a for loop,
							// but in the interest of keeping the Invoice.updateByIdWithJournalEntry calls
							// as similar as they can be to the original code we're keeping it this way.
							// TODO: Does this introduce performance considerations?
							Invoices.updateByIdWithJournalEntry(invoice._id, updateInvoiceQuery, {
								userId: currentUser._id,
								personId: person._id,
								orgId: currentUser.orgId, // Could be worth considering using the currentOrg?
								reason: `Voided credit memo ${matchedCreditMemo._id}`,
								reasonLocation: 'lib/methods.js:voidCreditMemo',
							});
						}
					}
				}

			});
			setQuery["billing.creditMemos.$.voidedAmount"] = parseFloat(matchedCreditMemo.originalAmount);
		}
		return await People.updateAsync({ _id: person._id, "billing.creditMemos._id": options.creditMemoId }, { $set: setQuery });
	},
	async getCreditMemoDetails(options) {

		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "read" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentUser = await Meteor.userAsync(),
			person = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId }),
			currentOrg = await Orgs.current();

		if (!person) throw new Meteor.Error(500, "Cannot find matching person");

		const matchedCreditMemo = _.chain(person)
			.deep("billing.creditMemos")
			.find(cm => cm._id == options.creditMemoId)
			.value();

		const cmInvoices = await Invoices.find({ orgId: currentOrg._id, "credits.creditMemoId": matchedCreditMemo._id }).fetchAsync(),
			applications = _.chain(cmInvoices)
				.map(i => _.filter(i.credits, c => {
					c.invoiceId = i._id;
					c.invoiceNumber = i.invoiceNumber;
					c.invoiceDate = i.invoiceDate;
					return c.creditMemoId == matchedCreditMemo._id;
				}))
				.flatten()
				.value();

		return { lineItems: applications };

	},
	async updateGroupBillingPlan(options) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser ||
			(await currentUser.fetchPerson())?.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const selectedPlan = _.find((await Orgs.current()).billing.plansAndItems, (i) => { return i._id == options.planId; });
		if (!selectedPlan) throw new Meteor.Error(500, "Improper billing plan selected");
		const selectedGroup = await Groups.findOneAsync({ _id: options.groupId });
		const existingPlanId = selectedGroup.defaultBillingPlanId;

		await Groups.updateAsync({ orgId: (await Orgs.current())._id, _id: options.groupId }, { $set: { "defaultBillingPlanId": selectedPlan._id } });
		if (options.replaceExisting) {
			if (existingPlanId) {
				await People.updateAsync({
					orgId: (await Orgs.current())._id,
					defaultGroupId: options.groupId
				}, { $pull: { "billing.enrolledPlans": { _id: existingPlanId } } }, { multi: true });
			}
			const newPlanData = {
				_id: selectedPlan._id,
				planDetails: selectedPlan,
				enrollmentDate: new moment().valueOf(),
				createdAt: new moment().valueOf(),
				allocations: [],
				createdBy: currentUser._id,
			};
			MiscUtils.setDocumentId(newPlanData, 'uid');
			await People.updateAsync({ orgId: (await Orgs.current())._id, defaultGroupId: options.groupId, type: "person", inActive: { $ne: true } }, {
				$push: {
					"billing.enrolledPlans": newPlanData
				}
			}, { multi: true });
		}
	},
	'modifyTimeCard': async function ({ timeCardId, checkInTime, checkOutTime, selectedPayTypeId }) {
		let description = `web time card management:: `;
		let currentUser = await Meteor.userAsync();
		let currentPerson = (currentUser) ? await currentUser.fetchPerson() : { type: null };

		if (!currentUser || currentPerson.type != "admin") {
			throw new Meteor.Error(403, "Access denied");
		}

		const org = await Orgs.current();

		await TimeCards.updateTimeCard({
			org,
			currentPerson,
			currentUser,
			timeCardId,
			checkInTime,
			checkOutTime,
			selectedPayTypeId,
			description,
		})
	},
	'getTimeCard': async function ({ timeCardId }) {
		let currentUser = await Meteor.userAsync();
		let currentPerson = (currentUser) ? await currentUser.fetchPerson() : { type: null };
		if (!currentUser || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		return await TimeCards.findOneAsync({ orgId: currentPerson.orgId, _id: timeCardId });
	},
	'insertImmunization': async function (immunizationData) {
		await processPermissions({
			assertions: [{ context: "people/profile/allergyImmunization", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		if (!((await People.findOneAsync(immunizationData.personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		check(immunizationData.immunizationType, String);
		check(immunizationData.immunizationDate, Number);
		let entry = { _id: Random.id(), type: immunizationData.immunizationType, createdByPersonId: currentUser.personId, createdAt: new Date().valueOf() };

		if (immunizationData.immunizationDefinitionId) {
			entry.immunizationDefinitionId = immunizationData.immunizationDefinitionId;
		}

		if (immunizationData.immunizationExemption) {
			entry.exemption = immunizationData.immunizationExemption;
			entry.date = null;
		} else {
			entry.date = DateTimeUtils.getDatestampInTimezone(immunizationData.immunizationDate, (await Orgs.current()).getTimezone());
			if (immunizationData.immunizationExemptDose) entry.exemptDose = true;

			if (immunizationData.extraDates) {
				const entryObjs = [];
				entryObjs.push(entry)
				for (let x = 0; x < immunizationData.extraDates.length; x++) {
					entryObjs.push({
						...entry,
						date: immunizationData.extraDates[x],
						_id: Random.id(),
					});
				}
				entry = { $each: entryObjs };
			}

		}

		const query = {
			"$addToSet": {
				"immunizationEntries":
					entry
			}
		};
		await People.updateAsync({ _id: immunizationData.personId }, query);
	},
	'editImmunization': async function ({ _id, date, personId }) {
		await processPermissions({
			assertions: [{ context: "people/profile/allergyImmunization", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin",
			throwError: true
		});

		var currentUser = await Meteor.userAsync();
		if (!((await People.findOneAsync(personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const query = { _id: personId, "immunizationEntries._id": _id };
		const updateSet = { $set: { "immunizationEntries.$.date": DateTimeUtils.getDatestampInTimezone(date, (await Orgs.current()).getTimezone()) } };
		await People.updateAsync(query, updateSet);
	},
	'removeImmunization': async function (immunizationData) {
		await processPermissions({
			assertions: [{ context: "people/profile/allergyImmunization", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin",
			throwError: true
		});
		var currentUser = await Meteor.userAsync();
		if (!((await People.findOneAsync(immunizationData.personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		check(immunizationData.immunizationType, String);

		let valid = true;

		const entry = { type: immunizationData.immunizationType };

		if (immunizationData.immunizationDefinitionId) {
			entry.immunizationDefinitionId = immunizationData.immunizationDefinitionId;
			delete entry.type;
		}

		if (immunizationData._id) {
			entry._id = immunizationData._id;
		} else if (immunizationData.immunizationDate) {
			entry.date = parseFloat(immunizationData.immunizationDate);
		} else if (immunizationData.immunizationExemption) {
			entry.exemption = { "$exists": true }
		} else {
			valid = false;
		}
		const query = {
			"$pull": {
				"immunizationEntries":
					entry
			}
		};

		if (valid)
			await People.updateAsync({ _id: immunizationData.personId }, query);
		else
			throw new Meteor.Error(500, "Invalid immunization entry removal request");
	},
	'insertMedication': async function (medicationData) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			!((await currentUser.fetchPerson())?.type == "admin") ||
			!((await People.findOneAsync(medicationData.personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		check(medicationData.name, String);
		check(medicationData.dosage, String);
		check(medicationData.notes, String);

		let newMedication = {
			_id: Random.id(),
			name: medicationData.name,
			dosage: medicationData.dosage,
			frequencyType: medicationData.frequencyType,
			durationType: medicationData.durationType,
			notes: medicationData.notes,
		};
		if (medicationData.frequencyAmount) {
			check(medicationData.frequencyAmount, Number);
			newMedication.frequencyAmount = medicationData.frequencyAmount;
		}
		if (medicationData.durationEnd) {
			check(medicationData.durationEnd, Number);
			newMedication.durationEnd = DateTimeUtils.getDatestampInTimezone(medicationData.durationEnd, (await Orgs.current()).getTimezone());
		}

		if (medicationData.medicationId)
			await People.updateAsync({ _id: medicationData.personId }, { "$pull": { "medicationEntries": { "_id": medicationData.medicationId } } });
		const query = { "$addToSet": { "medicationEntries": newMedication } };
		await People.updateAsync({ _id: medicationData.personId }, query);
	},
	'removeMedication': async function (medicationData) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			!((await currentUser.fetchPerson())?.type == "admin") ||
			!((await People.findOneAsync(medicationData.personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const query = {
			"$pull": {
				"medicationEntries":
					{ _id: medicationData.medicationId }
			}
		};
		await People.updateAsync({ _id: medicationData.personId }, query);
	},
	'updateStaffForecastClassList': async function ({ personId, staffForecast }) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			!((await currentUser.fetchPerson())?.type == "admin") ||
			!((await People.findOneAsync(personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const updateSet = {
			classList: {
				staffForecast,
			},
		};

		await People.updateAsync(personId, { $set: updateSet });

	},
	'updatePersonClassList': async function ({ personId, defaultDays, transitionDays, transitionGroupId, transitionGroupDate }) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			!((await currentUser.fetchPerson())?.type == "admin") ||
			!((await People.findOneAsync(personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const updateSet = {
			classList: {
				defaultDays,
				transitionDays,
			},
			transitionGroupDate,
			transitionGroupId,
		};

		await People.updateAsync(personId, { $set: updateSet });
	},
	'clearPersonTransitionClassList': async function ({ personId }) {
		var currentUser = await Meteor.userAsync();
		if (!currentUser ||
			!((await currentUser.fetchPerson())?.type == "admin") ||
			!((await People.findOneAsync(personId))["orgId"] == currentUser["orgId"]))
			throw new Meteor.Error(403, "Access denied");

		const updateSet = {
			"classList.transitionDays": {
				M: 0,
				T: 0,
				W: 0,
				R: 0,
				F: 0
			},
			transitionGroupDate: null,
			transitionGroupId: null,
		};

		await People.updateAsync(personId, { $set: updateSet });
	},

	async insertDiscountType(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const newType = (options.type || "").trim(), newDescription = (options.description || "").trim();
		if (newType == "") throw new Meteor.Error(500, "Type cannot be blank");

		let currentTypes = (await Orgs.current()).availableDiscountTypes(false);
		if (_.find(currentTypes, (t) => { return t.type == newType; })) throw new Meteor.Error(500, "The requested type code already exists");

		let newDiscount = { type: newType, description: newDescription };
		MiscUtils.setDocumentId(newDiscount);
		if (options.amount) {
			if (parseFloat(options.amount) < 0) throw new Meteor.Error(500, "Discount amount cannot be negative.");
			newDiscount["amount"] = parseFloat(options.amount);
			newDiscount["amountType"] = options.amountType;
		}
		if (options.expiresWithGracePeriod) newDiscount.expiresWithGracePeriod = options.expiresWithGracePeriod;
		if (options.overrideSingleDiscount) newDiscount.overrideSingleDiscount = options.overrideSingleDiscount;

		if ((await Orgs.current()).hasCustomization("billing/requireLedgerAccountName/enabled")) {
			const ledgerAccountName = options.ledgerAccountName;
			if (!ledgerAccountName) throw new Meteor.Error(500, "A ledger account is required to save this discount.");
			newDiscount.ledgerAccountName = ledgerAccountName
		}

		currentTypes.push(newDiscount);
		await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { "$set": { "valueOverrides.discountTypes": currentTypes } });
	},
	async removeDiscountType(discountType) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		let currentTypes = (await Orgs.current()).availableDiscountTypes(true);
		await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { "$set": { "valueOverrides.discountTypes": _.filter(currentTypes, (t) => { return t.type != discountType; }) } });
	},
	async archiveDiscountType(discountType) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentTypes = (await Orgs.current()).availableDiscountTypes(true),
			currentType = _.find(currentTypes, ct => ct.type == discountType),
			archiveValue = currentType.archived ? false : true;
		await Orgs.updateAsync({ _id: (await Orgs.current())._id, "valueOverrides.discountTypes.type": discountType },
			{ "$set": { "valueOverrides.discountTypes.$.archived": archiveValue } });
	},
	async updateDiscountType(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		let currentTypes = (await Orgs.current()).availableDiscountTypes(true);
		let currentType = _.find(currentTypes, (t) => { return t.type == options.type; });
		if (currentType) {
			currentType.description = options.description;
			currentType.amount = options.amount;
			if (currentType.amount && parseFloat(currentType.amount) < 0) throw new Meteor.Error(500, "Discount amount cannot be negative.");
			currentType.amountType = options.amountType;
			currentType.expiresWithGracePeriod = options.expiresWithGracePeriod;
			currentType.overrideSingleDiscount = options.overrideSingleDiscount;
			if ((await Orgs.current()).hasCustomization("billing/requireLedgerAccountName/enabled")) {
				const ledgerAccountName = options.ledgerAccountName;
				if (!ledgerAccountName) throw new Meteor.Error(500, "A ledger account is required to save this discount.");
				currentType.ledgerAccountName = ledgerAccountName
			}
			await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { "$set": { "valueOverrides.discountTypes": currentTypes } });
		}
	},
	async insertPayerType(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: person => person.type === "admin",
			throwError: true
		});

		const { type = "", description = "", ledgerAccountName, cashLedgerAccountName } = options;
		const newType = type.trim();
		const newDescription = description.trim();

		if (newType === "") {
			throw new Meteor.Error(500, "Code cannot be blank");
		}

		const currentTypes = (await Orgs.current()).availablePayerSources(true);
		const newTypeExists = currentTypes.some(currentType => currentType.type === newType);
		if (newTypeExists) {
			throw new Meteor.Error(500, "The requested payer code already exists");
		}

		const data = { type: newType, description: newDescription };

		if ((await Orgs.current()).hasCustomization("billing/requireLedgerAccountName/enabled")) {
			if (!ledgerAccountName) {
				throw new Meteor.Error(500, "A ledger account is required to save this payer.");
			}
			data.ledgerAccountName = ledgerAccountName;
		}

		if ((await Orgs.current()).hasCustomization(AvailableCustomizations.PAYER_CASH_LEDGER)) {
			data.cashLedgerAccountName = cashLedgerAccountName;
		}

		currentTypes.push(data);
		const currentOrgId = (await Orgs.current())._id;
		const updateQuery = { $set: { "valueOverrides.payerSources": currentTypes } };
		await Orgs.updateAsync({ _id: currentOrgId }, updateQuery);
	},
	async removePayerType(payerType) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		let currentTypes = (await Orgs.current()).availablePayerSources(true);
		await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { "$set": { "valueOverrides.payerSources": _.filter(currentTypes, (t) => { return t.type != payerType; }) } });
	},
	async archivePayerType(payerType) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		const currentTypes = (await Orgs.current()).availablePayerSources(true),
			currentType = _.find(currentTypes, ct => ct.type == payerType),
			archiveValue = currentType.archived ? false : true;
		await Orgs.updateAsync({ _id: (await Orgs.current())._id, "valueOverrides.payerSources.type": payerType },
			{ "$set": { "valueOverrides.payerSources.$.archived": archiveValue } });
	},
	async updatePayerType(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: person => person.type === "admin",
			throwError: true
		});

		const { type, description, ledgerAccountName, cashLedgerAccountName } = options;
		const currentTypes = (await Orgs.current()).availablePayerSources();
		const currentType = currentTypes.find(t => t.type === type);

		if (currentType) {
			if ((await Orgs.current()).hasCustomization("billing/requireLedgerAccountName/enabled")) {
				if (!ledgerAccountName) {
					throw new Meteor.Error(500, "A ledger account is required to save this payer.");
				}
				currentType.ledgerAccountName = ledgerAccountName;
			}

			if ((await Orgs.current()).hasCustomization(AvailableCustomizations.PAYER_CASH_LEDGER)) {
				currentType.cashLedgerAccountName = cashLedgerAccountName;
			}
			currentType.description = description;
			const currentOrgId = (await Orgs.current())._id;
			const updateQuery = { $set: { "valueOverrides.payerSources": currentTypes } };

			await Orgs.updateAsync({ _id: currentOrgId }, updateQuery);
		}
	},
	async insertProgram(options) {
		await processPermissions({
			assertions: [{ context: 'billing/configuration/system', action: 'edit' }],
			evaluator: (person) => person.type === 'admin',
			throwError: true
		});

		const newName = (options.name || '').trim(), isActive = options.isActive ?? false;
		if (newName === '') {
			throw new Meteor.Error(500, 'Name cannot be blank');
		}

		let currentPrograms = (await Orgs.current()).availablePrograms(true);
		if (_.find(currentPrograms, (p) => p.name === newName)) {
			throw new Meteor.Error(500, 'The requested program name already exists');
		}

		let newProgram = {
			_id: Random.id(),
			name: newName,
			isActive: isActive,
			isRequiredAdvanceNotice: options.isRequiredAdvanceNotice ?? false
		};

		await Orgs.updateAsync(
			{ _id: (await Orgs.current())._id },
			{ $addToSet: { 'billing.programs': newProgram } }
		);
	},
	async updateProgram(options) {
		await processPermissions({
			assertions: [{ context: 'billing/configuration/system', action: 'edit' }],
			evaluator: (person) => person.type == 'admin',
			throwError: true
		});

		let currentPrograms = (await Orgs.current()).availablePrograms(true);
		let currentProgram = _.find(currentPrograms, (p) => p._id === options._id);
		if (currentProgram) {
			const name = (options.name || '').trim();
			if (name === '') {
				throw new Meteor.Error(500, 'Name cannot be blank');
			}
			if (_.find(currentPrograms, (p) => p.name === name && p._id !== currentProgram._id)) {
				throw new Meteor.Error(500, 'There is another program with the same name. Please choose another name.');
			}
			currentProgram.name = options.name;
			currentProgram.isActive = options.isActive ?? false;
			currentProgram.isRequiredAdvanceNotice = options.isRequiredAdvanceNotice ?? false;
			await Orgs.updateAsync(
				{ _id: (await Orgs.current())._id },
				{ $set: { 'billing.programs': currentPrograms } }
			);
		}
	},
	async insertTimePeriod(options) {
		await processPermissions({
			assertions: [{ context: 'billing/configuration/system', action: 'edit' }],
			evaluator: (person) => person.type === 'admin',
			throwError: true
		});
		const timezone = (await Orgs.current()).getTimezone();
		const newName = (options.name || '').trim();
		const startDate = new moment.tz(options.startDate, timezone).startOf('day').unix() * 1000 || null;
		const endDate = new moment.tz(options.endDate, timezone).startOf('day').unix() * 1000 || null;
		if (newName === '') {
			throw new Meteor.Error(500, 'Name cannot be blank');
		}
		if (!startDate) {
			throw new Meteor.Error(500, 'Must provide a valid start date');
		}
		if (!endDate) {
			throw new Meteor.Error(500, 'Must provide a valid end date');
		}

		const currentTimePeriods = (await Orgs.current()).availableTimePeriods();
		if (_.find(currentTimePeriods, (tp) => tp.name === newName)) {
			throw new Meteor.Error(500, 'The requested time period name already exists');
		}
		if (_.find(currentTimePeriods, (tp) => (tp.startDate <= startDate && tp.endDate >= startDate) || (tp.startDate <= endDate && tp.endDate >= endDate) || (tp.startDate > startDate && tp.endDate < endDate))) {
			throw new Meteor.Error(500, 'Dates must not overlap with other time periods');
		}


		let newTimePeriod = {
			_id: Random.id(),
			name: newName,
			startDate: startDate,
			endDate: endDate
		};

		await Orgs.updateAsync(
			{ _id: (await Orgs.current())._id },
			{ $addToSet: { 'billing.timePeriods': newTimePeriod } }
		);
	},
	async updateTimePeriod(options) {
		await processPermissions({
			assertions: [{ context: 'billing/configuration/system', action: 'edit' }],
			evaluator: (person) => person.type === 'admin',
			throwError: true
		});

		let currentTimePeriods = (await Orgs.current()).availableTimePeriods();
		let currentTimePeriod = _.find(currentTimePeriods, (tp) => tp._id === options._id);
		if (currentTimePeriod) {
			const timezone = (await Orgs.current()).getTimezone();
			const newName = (options.name || '').trim();
			const startDate = new moment.tz(options.startDate, timezone).startOf('day').unix() * 1000 || null;
			const endDate = new moment.tz(options.endDate, timezone).startOf('day').unix() * 1000 || null;
			if (newName === '') {
				throw new Meteor.Error(500, 'Name cannot be blank');
			}
			if (!startDate) {
				throw new Meteor.Error(500, 'Must provide a valid start date');
			}
			if (!endDate) {
				throw new Meteor.Error(500, 'Must provide a valid end date');
			}

			if (_.find(currentTimePeriods, (tp) => tp.name === newName && tp._id !== currentTimePeriod._id)) {
				throw new Meteor.Error(500, 'There is another time period with the same name. Please choose another name.');
			}

			if (_.find(currentTimePeriods, (tp) => (tp._id !== currentTimePeriod._id && ((tp.startDate <= startDate && tp.endDate >= startDate) || (tp.startDate <= endDate && tp.endDate >= endDate) || (tp.startDate > startDate && tp.endDate < endDate))))) {
				throw new Meteor.Error(500, 'Dates must not overlap with other time periods');
			}

			currentTimePeriod.name = newName;
			currentTimePeriod.startDate = startDate;
			currentTimePeriod.endDate = endDate;
			await Orgs.updateAsync(
				{ _id: (await Orgs.current())._id },
				{ $set: { 'billing.timePeriods': currentTimePeriods } }
			);
		}
	},
	async updateBillingMap(options) {
		await processPermissions({
			assertions: [{ context: "billing/configuration/system", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		const o = await Orgs.current();
		if (!o.billingMaps() || !o.billingMaps()[options.code]) throw new Meteor.Error(500, "Invalid billing map code.");

		const updateKey = "billing.billingMaps." + options.code + ".accountName",
			updateQuery = { "$set": {} };
		updateQuery["$set"][updateKey] = options.accountName;
		await Orgs.updateAsync({ _id: o._id }, updateQuery);
	},
	async suspendPersonBilling(options) {
		await processPermissions({
			assertions: [{ context: "billing/invoices", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});

		let setQuery = {};
		const currentPerson = await People.findOneAsync({ _id: options.personId, orgId: (await Orgs.current())._id });

		if (options.suspendUntil) {
			check(options.suspendUntil, Number);
			setQuery["$set"] = { "billing.suspendUntil": DateTimeUtils.getDatestampInTimezone(options.suspendUntil, (await Orgs.current()).getTimezone()) };
		} else {
			setQuery["$unset"] = { "billing.suspendUntil": 1 };
		}

		if (currentPerson)
			await People.updateAsync({ _id: options.personId }, setQuery);
	},
	async insertMessage(options) {
		let currentUser = await Meteor.userAsync(), currentPerson = await currentUser?.fetchPerson();
		if (!currentUser || !currentPerson ||
			!_.contains(["admin", "staff", "family"], currentPerson.type))
			throw new Meteor.Error(403, "Access denied");

		// make sure all recipients in org and messageable from this person
		let newMessage = {}, newId, insertId;
		const org = await Orgs.current();
		if (options.threadId) {
			const originalThread = await Messages.findOneAsync({ orgId: org._id, _id: options.threadId });

			const replyContent = {
				personId: currentUser.personId,
				createdAt: new moment().valueOf(),
				createdBy: currentUser._id,
				message: options.message
			};

			// Find all family members to be in the thread if include all family is checked
			let allFamilyIds = [];
			if (options.includeAllFamily && !originalThread.includeAllFamily) {
				const curPersonFamilyPeople = await currentPerson.findRelatedFamilyPeople(false, true);
				allFamilyIds = await curPersonFamilyPeople.mapAsync((p) => p._id);
				if (allFamilyIds.length) {
					// Don't include the current person in the array
					const personIndex = allFamilyIds.indexOf(currentPerson._id);
					if (personIndex > -1) {
						allFamilyIds.splice(personIndex, 1);
					}
				}
			}

			let staffIdsInThread = [];
			if (currentPerson.type === 'family' && !options.excludeStaff) {
				// If there are any staff members in the thread, keep them in the recipients
				const staffInThread = await People.find(
					{
						_id: { $in: originalThread.currentRecipients },
						orgId: org._id,
						type: { $in: ['staff', 'admin'] }
					},
					{
						fields: { _id: 1 }
					}
				).fetchAsync();
				staffIdsInThread = staffInThread.map((p) => p._id);
			} else {
				const staffInThread = await People.find(
					{
						_id: { $in: originalThread.currentRecipients },
						orgId: org._id,
						type: { $in: ['admin'] }
					},
					{
						fields: { _id: 1 }
					}
				).fetchAsync();
				staffIdsInThread = staffInThread.map((p) => p._id);
			}

			if (currentPerson.type === 'family' && options.includeAllFamily) {
				// Determine if a new thread needs to be created if the thread includes people outside of the family members found.
				// A new thread only needs to be created when first including other family members, not when the thread has already been marked so.
				// Don't make a new thread when the recipients have been locked.
				let makeNewThread = false;
				if (
					originalThread.currentRecipients.length > 1 &&
					currentPerson._id !== originalThread.personId &&
					!originalThread.includeAllFamily &&
					!originalThread.lockRecipients
				) {
					for (const id of originalThread.currentRecipients) {
						if (!allFamilyIds.includes(id) && id !== currentPerson._id && id !== originalThread.personId && !staffIdsInThread.includes(id)) {
							makeNewThread = true;
							break;
						}
					}
				}
				if (!makeNewThread) {
					await Messages.updateAsync(
						{ _id: options.threadId },
						{
							'$addToSet': { 'replies': replyContent },
							'$unset': {
								'markedArchived': 1
							},
							'$set': {
								'lastActivity': new moment().valueOf(),
								'markedAsRead': [currentUser.personId],
								'includeAllFamily': true,
								'lockRecipients': true
							}
						}
					);
					// Make sure all family members are in the thread
					if (!originalThread.includeAllFamily) {
						await Messages.updateAsync({ _id: options.threadId }, { '$addToSet': { 'currentRecipients': { $each: allFamilyIds } } });
					}
				} else if (makeNewThread || options.excludeStaff) {
					// Create a new thread when replying back to a message that was to multiple people and the user wants to include their family
					newMessage = JSON.parse(JSON.stringify(originalThread));
					newMessage.originalThreadId = options.threadId;
					delete newMessage._id;
					newMessage.currentRecipients = [currentUser.personId].concat(...allFamilyIds, ...staffIdsInThread);
					newMessage.originalRecipients = [currentUser.personId];
					newMessage.subject = "Re: " + originalThread.subject;
					if (newMessage.replies) {
						newMessage.replies.push(replyContent);
					} else {
						newMessage.replies = [replyContent];
					}
					newMessage.lastActivity = new moment().valueOf();
					newMessage.markedAsRead = [currentUser.personId];
					newMessage.markedArchived = [];
					newMessage.includeAllFamily = true;
					newMessage.lockRecipients = true;
					newMessage.excludeStaff = options?.excludeStaff;
					newId = await Messages.insertAsync(newMessage);

					// Remove the family members from the old thread
					const removeIds = [currentUser.personId];
					removeIds.push(...allFamilyIds);
					await Messages.updateAsync({ _id: options.threadId }, { $pull: { currentRecipients: { $in: removeIds } } });
				}
			} else if (
				originalThread.currentRecipients.length > 1 &&
				originalThread.personId != currentUser.personId &&
				!originalThread.includeAllFamily &&
				(!originalThread.lockRecipients || options.excludeStaff)
			) {
				// If this is a family person and is the only non-staff/admin in the thread, just add the reply to the thread
				if (!options.excludeStaff && currentPerson.type === 'family' && originalThread.currentRecipients.length === (staffIdsInThread.length + 1)) {
					await Messages.updateAsync(
						{ _id: options.threadId },
						{
							'$addToSet': { 'replies': replyContent },
							'$unset': {
								'markedArchived': 1
							},
							'$set': {
								'lastActivity': new moment().valueOf(),
								'markedAsRead': [currentUser.personId],
								'lockRecipients': true
							}
						}
					);
				} else {
					// Create a new thread
					newMessage = JSON.parse(JSON.stringify(originalThread));
					newMessage.originalThreadId = options.threadId;
					delete newMessage._id;
					newMessage.currentRecipients = [currentUser.personId];
					if (currentPerson.type === 'family') {
						// Keep all staff in the thread and lock the recipients
						newMessage.currentRecipients.push(...staffIdsInThread);
						newMessage.lockRecipients = true;
					}
					newMessage.subject = "Re: " + originalThread.subject;
					if (newMessage.replies) {
						newMessage.replies.push(replyContent);
					} else {
						newMessage.replies = [replyContent];
					}
					newMessage.lastActivity = new moment().valueOf();
					newMessage.markedAsRead = [currentUser.personId];
					newMessage.markedArchived = [];
					newMessage.excludeStaff = options?.excludeStaff;
					newId = await Messages.insertAsync(newMessage);

					await Messages.updateAsync({ _id: options.threadId }, { $pull: { currentRecipients: currentUser.personId } });
				}
			} else {
				await Messages.updateAsync({ _id: options.threadId }, { "$addToSet": { "replies": replyContent } });
				await Messages.updateAsync({ _id: options.threadId }, {
					"$unset": {
						"markedArchived": 1
					},
					"$set": {
						"lastActivity": new moment().valueOf(),
						"markedAsRead": [currentUser.personId],
						"excludeStaff": options.excludeStaff
					}
				});
			}

		} else {
			let parsedRecipients = [];
			const designations = org?.valueOverrides?.designations ?? [];
			const hideStaff = org && org.hasCustomization('messages/disableStaffMessages/enabled');
			let lockRecipients = false;
			for (const recipientId of options.recipients) {
				// If a parent is creating a message, always lock the thread since they can only send to staff or admins unless they excluded staff
				if (currentPerson.type === 'family' && !options.excludeStaff) {
					lockRecipients = true;
				}
				if (_.contains(["staff", "admin"], currentPerson.type) && recipientId == "group:allstaff") {
					const getStaff = await People.find({ orgId: currentUser.orgId, inActive: { $ne: true }, type: "staff" }).mapAsync((p) => p._id);
					parsedRecipients.push(...getStaff);
				} else if (_.contains(['staff', 'admin', 'family'], currentPerson.type) && recipientId == 'group:alladmins') {
					const getAdmin = await People.find({ orgId: currentUser.orgId, inActive: { $ne: true }, type: 'admin' }).mapAsync((p) => p._id);
					parsedRecipients.push(...getAdmin);
				} else if (_.contains(['staff', 'admin'], currentPerson.type) && recipientId == 'group:allfamilies') {
					const childIds = await People.find({ orgId: currentUser.orgId, inActive: { $ne: true }, type: 'person' }).mapAsync((p) => p._id);
					const relatedPeopleIds = await Relationships.find({ orgId: currentUser.orgId, relationshipType: 'family', targetId: { $in: childIds } }, { fields: { personId: 1 } }).mapAsync((p) => p.personId);
					const relatedActivePeopleIds = await People.find({ orgId: currentUser.orgId, _id: { $in: relatedPeopleIds }, inActive: { $ne: true } }, { fields: { _id: 1 } }).mapAsync((p) => p._id);
					parsedRecipients.push(...relatedActivePeopleIds);
				} else if (currentPerson.type === 'admin' && recipientId === 'group:missingScheduled') {

					if (Meteor.isServer) {
						const childIds = (await getTodaysMissingChildren({ orgId: currentUser.orgId })).map(p => p._id);
						const relatedPeopleIds = await Relationships.find(
							{
								orgId: currentUser.orgId,
								relationshipType: 'family',
								targetId: { '$in': childIds }
							},
							{ fields: { personId: 1 } }
						).mapAsync(p => p.personId);
						const relatedActivePeopleIds = await People.find(
							{
								orgId: currentUser.orgId,
								_id: { '$in': relatedPeopleIds },
								inActive: { $ne: true }
							},
							{ fields: { _id: 1 } }
						).mapAsync(p => p._id);
						parsedRecipients.push(...relatedActivePeopleIds);
					}
				} else if (currentPerson.type === 'family' && recipientId.startsWith('group:allteachers:')) {
					const childId = recipientId.replace('group:allteachers:', '');
					const child = await People.findOneAsync({ _id: childId });
					const groupId = child.checkedIn && child.checkInGroupId ? child.checkInGroupId : child.defaultGroupId;
					if (groupId) {
						const teachers = await People.find({ orgId: currentUser.orgId, inActive: { $ne: true }, type: { $in: ['staff', 'admin'] } }).fetchAsync();
						for (const teacher of teachers) {
							if ((teacher.checkedIn && teacher.checkInGroupId ? teacher.checkInGroupId : teacher.defaultGroupId) === groupId) {
								parsedRecipients.push(teacher._id);
							}
						}
					}
				} else if (_.contains(["staff", "admin"], currentPerson.type) && recipientId.startsWith("group:")) {
					const groupId = recipientId.replace("group:", "");
					const groupPeopleQuery = {
						orgId: currentUser.orgId,
						defaultGroupId: groupId,
						inActive: { "$ne": true }
					}
					if (hideStaff) {
						groupPeopleQuery['type'] = { $ne: 'staff' };
					}
					const groupPeople = await People.find(groupPeopleQuery, { fields: { _id: 1, type: 1 } }).mapAsync((p) => ({ _id: p._id, type: p.type })),
						groupPeopleIds = groupPeople.filter((gp) => gp.type == "person").map((gp) => gp._id),
						staffAdminPeopleIds = groupPeople.filter((gp) => gp.type != "person").map((gp) => gp._id),
						relatedPeopleIds = await Relationships.find({ orgId: currentUser.orgId, relationshipType: "family", targetId: { "$in": groupPeopleIds } }, { fields: { personId: 1 } }).mapAsync((p) => p.personId),
						relatedActivePeopleIds = await People.find({ orgId: currentUser.orgId, _id: { "$in": relatedPeopleIds }, inActive: { $ne: true } }, { fields: { _id: 1 } }).mapAsync((p) => p._id);
					parsedRecipients.push(...relatedActivePeopleIds);
					parsedRecipients.push(...staffAdminPeopleIds);
				} else if (_.contains(["admin"], currentPerson.type) && recipientId.startsWith("designation:")) {
					const designation = recipientId.replace("designation:", "");
					const foundDesignation = _.find(designations, (d) => d.replace(/\s/g, '') == designation);
					if (foundDesignation) {
						const designationPeople = await People.find({ orgId: currentUser.orgId, designations: { "$in": [foundDesignation] }, inActive: { "$ne": true } }, { fields: { _id: 1, type: 1 } }).mapAsync((p) => ({ _id: p._id, type: p.type })),
							designationPeopleIds = designationPeople.filter((gp) => gp.type == "person").map((gp) => gp._id),
							designationRelatedPeopleIds = await Relationships.find({ orgId: currentUser.orgId, relationshipType: "family", targetId: { "$in": designationPeopleIds } }, { fields: { personId: 1 } }).mapAsync((p) => p.personId),
							designationRelatedActivePeopleIds = await People.find({ orgId: currentUser.orgId, _id: { "$in": designationRelatedPeopleIds }, inActive: { $ne: true } }, { fields: { _id: 1 } }).mapAsync((p) => p._id);
						parsedRecipients.push(...designationRelatedActivePeopleIds);
					}
				} else {
					const p = await People.findOneAsync({ _id: recipientId, orgId: currentUser.orgId });
					if (p) {
						if (p.type == "person") {
							const relationships = await Relationships.find({ orgId: currentUser.orgId, relationshipType: "family", targetId: recipientId }).fetchAsync();
							parsedRecipients = parsedRecipients.concat(_.pluck(relationships, "personId"));
							parsedRecipients = (await People.find({ _id: { $in: parsedRecipients }, inActive: { $ne: true } }, { fields: { _id: 1 } }).fetchAsync()).map((p) => p._id);
						} else
							parsedRecipients.push(recipientId);
					}
				}
			};

			newMessage = {
				originalRecipients: options.recipients,
				currentRecipients: _.without(parsedRecipients, currentPerson._id),
				subject: options.subject,
				createdAt: new moment().valueOf(),
				createdBy: currentUser._id,
				orgId: (await Orgs.current())._id,
				personId: currentUser.personId,
				message: options.message,
				lastActivity: new moment().valueOf(),
				markedAsRead: [currentUser.personId],
				markedArchived: [currentUser.personId],
				lockRecipients: lockRecipients,
				includeAllFamily: options?.includeAllFamily,
				excludeStaff: options.excludeStaff
			};
			insertId = await Messages.insertAsync(newMessage);
		};
		if (Meteor.isServer)
			Meteor.defer(async function () {
				await processMessageNotifications(newId || insertId || options.threadId);
			});

		return { newId: newId };
	},
	async archiveMessage(options) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin", "staff", "family"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		let messageIds;
		if (options.messageIds)
			messageIds = options.messageIds;
		else
			messageIds = [options.messageId];

		for (const messageId of messageIds) {
			const message = await Messages.findOneAsync({ orgId: currentUser.orgId, _id: messageId });
			if (message && (message.personId == currentUser.personId || _.contains(message.currentRecipients, currentUser.personId)) &&
				!_.contains(message.markedArchived, currentUser.personId)) {
				await Messages.updateAsync({ _id: messageId }, { "$addToSet": { markedArchived: currentUser.personId } });
				await NewMessages.upsertAsync({ personId: currentUser.personId }, { $set: { hasNotification: false } });
			}
		}
	},
	async unarchiveMessage(options) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin", "staff", "family"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const message = await Messages.findOneAsync({ orgId: currentUser.orgId, _id: options.messageId });
		if ((message.personId == currentUser.personId || _.contains(message.currentRecipients, currentUser.personId)) &&
			_.contains(message.markedArchived, currentUser.personId))
			await Messages.updateAsync({ _id: options.messageId }, { "$pull": { markedArchived: currentUser.personId } });
	},
	async insertUpdateDocumentRepository(options) {
		let currentUser = await Meteor.userAsync();

		await processPermissions({
			assertions: [{ context: "documents", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		if (!options.name) throw new Meteor.Error(500, "A name must be specified for the new document type");

		const org = await Orgs.findOneAsync({ _id: currentUser.orgId });
		if (!options.documentId && org.documentDefinitions && _.find(org.documentDefinitions, (d) => { return d.name == options.name; }))
			throw new Meteor.Error(500, "A document by that name already exists.");

		let repositoryKey, doc, query, selector;

		if (options.documentId) {
			doc = org.documentDefinitions && _.find(org.documentDefinitions, (d) => { return d._id == options.documentId; });
		} else {
			doc = {
				_id: Random.id(),
				createdByPersonId: currentUser.personId,
				createdByUserId: currentUser.userId,
				createdAt: new Date().valueOf()
			};
		}
		if (options.masterTemplateKey) doc.repositoryKey = currentUser.orgId + "/" + currentUser._id + "/" + options.masterTemplateKey;
		doc.name = options.name;
		doc.section = options.section;
		doc.templateOption = options.templateOption;
		doc.selectedGroupIds = options.assignmentType == "groups" ? options.selectedGroupIds : null;
		doc.assignmentType = options.assignmentType;
		doc.hideNotUploaded = options.hideNotUploaded;

		if (options.documentId) {
			query = { "$set": { "documentDefinitions.$": doc } };
			selector = { _id: org._id, "documentDefinitions._id": options.documentId };
		} else {
			query = { "$addToSet": {} };
			query["$addToSet"]["documentDefinitions"] = doc;
			selector = { _id: org._id };
		}
		await Orgs.updateAsync(selector, query);
	},
	async removeDocumentRepository(options) {
		let currentUser = await Meteor.userAsync();
		await processPermissions({
			assertions: [{ context: "documents", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "staff",
			throwError: true
		});

		await Orgs.updateAsync({ _id: currentUser.orgId, "documentDefinitions._id": options.documentId },
			{
				"$set": {
					"documentDefinitions.$.deletedAt": new Date().valueOf(),
					"documentDefinitions.$.deletedByPersonId": currentUser.personId,
					"documentDefinitions.$.deletedByUserId": currentUser._id,
				}
			}
		);
	},
	async insertUpdateHoliday(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		if (data.existingId) {
			const updateHolidayData = {
				"valueOverrides.holidays.$.name": data.holidayName,
				"valueOverrides.holidays.$.date": data.holidayDate
			};

			await Orgs.updateAsync({ _id: currentUser.orgId, "valueOverrides.holidays._id": data.existingId }, { $set: updateHolidayData });
		} else {
			const newHoliday = {
				_id: Random.id(),
				name: data.holidayName,
				date: data.holidayDate
			};
			await Orgs.updateAsync({ _id: currentUser.orgId }, { $addToSet: { "valueOverrides.holidays": newHoliday } });
		}
	},
	async removeHoliday(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const currentTypes = (await Orgs.current()).getHolidays();

		await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { "$set": { "valueOverrides.holidays": _.filter(currentTypes, (t) => { return t._id != data.holidayId; }) } });

	},
	async insertBrand(data) {
		if (!data.orgId) {
			throw new Meteor.Error(403, "No Org Id");
		}
		const currentUser = await Meteor.userAsync();
		if (!currentUser || (await currentUser.fetchPerson())?.type !== "admin" || !(await currentUser.fetchPerson())?.superAdmin) {
			throw new Meteor.Error(403, "Access denied");
		}
		const org = await Orgs.findOneAsync({ _id: data.orgId })

		if (data.existingId) {
			const updateBrandData = {
				"brands.$.name": data.brandName
			};
			await Orgs.updateAsync({ _id: data.orgId, "brands._id": data.existingId }, { $set: updateBrandData });
			return { _id: data.existingId, name: data.brandName };
		} else {
			const newBrand = {
				_id: Random.id(),
				name: data.brandName
			};

			if (!org.getBrands()) {
				await Orgs.updateAsync({ _id: data.orgId }, { $set: { "brands": [newBrand] } });
			} else {
				await Orgs.updateAsync({ _id: data.orgId }, { $addToSet: { "brands": newBrand } });
			}
			return newBrand;
		}
	},
	async removeBrand(data) {
		if (!data.orgId) {
			throw new Meteor.Error(403, "No Org Id");
		}
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.findOneAsync({ _id: data.orgId });
		const currentBrands = org.getBrands();

		await Orgs.updateAsync({ _id: data.orgId }, { "$set": { "brands": _.filter(currentBrands, (t) => { return t._id !== data.brandId; }) } });
		//remove selectedBrands from orgs that had it assigned
		const allOrgs = await Orgs.find({ selectedBrand: data.brandId }).fetchAsync();
		for (const org of allOrgs) {
			await Orgs.updateAsync({ _id: org._id }, { $unset: { selectedBrand: 1 } });
		}

	},
	async assignSites(data) {
		if (!data.id || !data.value) {
			throw new Meteor.Error(400, "Invalid Data");
		}
		const currentUser = await Meteor.userAsync();
		if (!currentUser || (await currentUser.fetchPerson())?.type !== "admin" || !(await currentUser.fetchPerson())?.superAdmin) {
			throw new Meteor.Error(403, "Access denied");
		}
		if (data.value === 'on') {
			await Orgs.updateAsync({ _id: data.id }, { $unset: { selectedBrand: 1 } });
		} else {
			await Orgs.updateAsync({ _id: data.id }, { $set: { selectedBrand: data.value } });
		}
	},
	async addSchool(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type)) {
			throw new Meteor.Error(403, "Access denied");
		}
		const newSchool = data;
		const org = await Orgs.findOneAsync({ _id: (await Orgs.current())._id });
		const schoolFields = org.valueOverrides?.profileFields?.find(field => field.name === "school" && field.type === "select");
		const existingSchoolValues = schoolFields?.values ?? [];
		existingSchoolValues.push(newSchool.schoolName);
		if (schoolFields) {
			await Orgs.updateAsync(
				{
					_id: (await Orgs.current())._id,
					"valueOverrides.profileFields.name": "school"
				},
				{ $set: { "valueOverrides.profileFields.$.values": existingSchoolValues } }
			);
		} else {
			await Orgs.updateAsync(
				{
					_id: (await Orgs.current())._id,
				},
				{ $addToSet: { "valueOverrides.profileFields": { name: 'school', type: 'select', values: existingSchoolValues } } }
			);
		}
	},
	async updateSchool(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type)) {
			throw new Meteor.Error(403, "Access denied");
		}
		const existingSchools = await Orgs.findOneAsync({
			_id: (await Orgs.current())._id,
			"valueOverrides.profileFields": {
				$elemMatch: {
					name: "school",
					type: "select"
				}
			}
		});
		const existingSchoolValues = [];
		existingSchools.valueOverrides.profileFields.forEach(field => {
			if (field.name === "school" && field.type === "select") {
				existingSchoolValues.push(...field.values.sort((a, b) => a.localeCompare(b)));
			}
		});
		const updatedSchoolValue = existingSchoolValues.map((val, index) => {
			return (index === Number(data.index) ? data.schoolName : val);
		});
		await Orgs.updateAsync(
			{
				_id: (await Orgs.current())._id,
				"valueOverrides.profileFields.name": "school"
			},
			{ $set: { "valueOverrides.profileFields.$.values": updatedSchoolValue } }
		);
	},
	async removeSchool(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");
		const existingSchools = await Orgs.findOneAsync({
			_id: (await Orgs.current())._id,
			"valueOverrides.profileFields": {
				$elemMatch: {
					name: "school",
					type: "select"
				}
			}
		});
		const existingSchoolValues = [];
		existingSchools.valueOverrides.profileFields.forEach(field => {
			if (field.name === "school" && field.type === "select") {
				existingSchoolValues.push(...field.values.sort((a, b) => a.localeCompare(b)));
			}
		});
		const filteredSchools = existingSchoolValues.filter((value) => {
			return existingSchoolValues.indexOf(value) !== Number(data.index);
		});
		await Orgs.updateAsync(
			{
				_id: (await Orgs.current())._id,
				"valueOverrides.profileFields": {
					$elemMatch: {
						name: "school",
						type: "select"
					}
				}
			},
			{ "$set": { "valueOverrides.profileFields.$.values": filteredSchools } });
	},
	async insertUpdateBusRoute(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		if (data.existingId) {
			const updateBusRouteData = {
				"busRoutes.$.name": data.busRouteName,
				"busRoutes.$.am": data.busRouteAm,
				"busRoutes.$.pm": data.busRoutePm
			};

			await Orgs.updateAsync({ _id: currentUser.orgId, "busRoutes._id": data.existingId }, { $set: updateBusRouteData });
		} else {
			const newBusRoute = {
				_id: Random.id(),
				name: data.busRouteName,
				am: data.busRouteAm,
				pm: data.busRoutePm
			};
			await Orgs.updateAsync({ _id: currentUser.orgId }, { $addToSet: { "busRoutes": newBusRoute } });
		}
	},
	async removeBusRoute(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const currentBusRoutes = (await Orgs.current()).getBusRoutes();

		await Orgs.updateAsync({ _id: (await Orgs.current())._id }, { "$set": { "busRoutes": _.filter(currentBusRoutes, (t) => { return t._id != data.busRouteId; }) } });
	},
	async updateGradeProfileFields(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type)) {
			throw new Meteor.Error(403, "Access denied");
		} else {
			const value = {
				"name": "studentGrade",
				"description": "Student Grades",
				"type": "select",
				"values": data ?? []
			}
			const org = await Orgs.findOneAsync({ _id: currentUser.orgId });
			const studentGrade = org?.valueOverrides?.profileFields?.filter((elem) => {
				if (elem.name === 'studentGrade') return elem;
			});
			if (studentGrade !== undefined && studentGrade.length > 0) {
				await Orgs.updateAsync(
					{ _id: currentUser.orgId },
					{ $set: { "valueOverrides.profileFields.$[elem].values": data } },
					{ arrayFilters: [{ "elem.name": "studentGrade" }] }
				);
			} else {
				await Orgs.updateAsync(
					{ _id: currentUser.orgId },
					{ $push: { "valueOverrides.profileFields": value } },
				);
			};
		}
	},
	async insertImmunizationDefinition(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const newImmunizationType = {
			_id: Random.id(),
			type: data.type,
			description: data.description,
		}

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $addToSet: { immunizationDefinitions: newImmunizationType } });
	},
	async saveStandardImmunization(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();
		const immunizations = org.availableImmunizations();
		const immunizationToUpdate = _.find(immunizations, (i) => {
			return i.type == data.type
		})

		if (immunizationToUpdate) {
			const existingOverride = _.find(org.immunizationOverrides || [], (i) => {
				return i.type == data.type;
			});

			if (existingOverride) {
				const updateImmunizationData = {
					"immunizationOverrides.$.type": data.type,
					"immunizationOverrides.$.exempt": data.exempt,
					"immunizationOverrides.$.annual": data.annual,
				};

				await Orgs.updateAsync({ _id: currentUser.orgId, "immunizationOverrides.type": data.type }, { $set: updateImmunizationData });
			} else {
				const addToSetUpdate = {
					type: data.type,
					exempt: data.exempt,
					annual: data.annual,
				};
				await Orgs.updateAsync({ _id: currentUser.orgId }, { $addToSet: { immunizationOverrides: addToSetUpdate } })
			}
		}
	},
	async updateImmunizationDefinition(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const archived = (data.archived) ? true : false;
		const annual = (data.annual) ? true : false;
		const updateImmunizationData = {
			"immunizationDefinitions.$.type": data.type,
			"immunizationDefinitions.$.archived": archived,
			"immunizationDefinitions.$.annual": annual,
			"immunizationDefinitions.$.description": data.description,
		};

		await Orgs.updateAsync({ _id: currentUser.orgId, "immunizationDefinitions._id": data._id }, { $set: updateImmunizationData });
		await People.updateAsync({ orgId: currentUser.orgId, "immunizationEntries.immunizationDefinitionId": data._id }, { $set: { "immunizationEntries.$.type": data.type, "immunizationEntries.$.archived": archived } })
	},
	async saveCustomPaySettings(data) {
		const { overtimeThreshold, overtimeFrequency } = data;
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const active = (data.overtimeActive) ? true : false;
		const updateData = {
			"customStaffPaySettings.overtimeThreshold": overtimeThreshold,
			"customStaffPaySettings.overtimeActive": active,
			"customStaffPaySettings.frequency": overtimeFrequency,
		};

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: updateData });
	},
	async saveChatSupportSettings({ chatUrl, chatSupportActive, menuVerbiage }) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const enabled = (chatSupportActive) ? true : false;
		const updateData = {
			"customizations.people/chatSupport/enabled": enabled,
			"chatSupportSettings.chatUrl": chatUrl,
			"chatSupportSettings.menuVerbiage": menuVerbiage,
		};
		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: updateData });
	},
	async insertCustomPayType(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const newCustomPayType = {
			_id: Random.id(),
			type: data.type,
			rate: data.rate,
			staffProfileRate: data.staffProfileRate,
			staffOTEligible: data.staffOTEligible,
		}
		if (data.adpCode) {
			newCustomPayType.adpCode = data.adpCode;
		}

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $addToSet: { "customStaffPaySettings.types": newCustomPayType } });
	},
	async updateStandardPayAdpCode(code) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		await Orgs.updateAsync({ _id: currentUser.orgId }, { $set: { 'customStaffPaySettings.standardPayAdpCode': code } });
	},
	async updateCustomPayType(data) {
		let currentUser = await Meteor.userAsync();
		if (!currentUser || !_.contains(["admin"], (await currentUser.fetchPerson())?.type))
			throw new Meteor.Error(403, "Access denied");

		const archived = (data.archived) ? true : false;
		const staffProfileRate = (data.staffProfileRate) ? true : false;
		const staffOTEligible = (data.staffOTEligible) ? true : false;
		const updateCustomPayType = {
			"customStaffPaySettings.types.$.type": data.type,
			"customStaffPaySettings.types.$.archived": archived,
			"customStaffPaySettings.types.$.rate": data.rate,
			"customStaffPaySettings.types.$.staffProfileRate": staffProfileRate,
			"customStaffPaySettings.types.$.staffOTEligible": staffOTEligible,
		};
		if (data.adpCode) {
			updateCustomPayType["customStaffPaySettings.types.$.adpCode"] = data.adpCode;
		}

		await Orgs.updateAsync({ _id: currentUser.orgId, "customStaffPaySettings.types._id": data._id }, { $set: updateCustomPayType });
	},
	async updateExpressDriveUpSettings(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.();
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const enabled = options.enabled;
		const arrivalText = options.arrivalText;

		const org = await Orgs.current();
		const canUpdateExpressDriveUp = !org.hasCustomization("people/familyCheckin/enabled");
		if (canUpdateExpressDriveUp) await Orgs.updateAsync({ _id: org._id }, { $set: { "customizations.people/expressDriveUp/enabled": enabled } });
		if (arrivalText.trim().length > 0) {
			await Orgs.updateAsync({ _id: org._id }, { $set: { "valueOverrides.expressDriveUp.arrivalText": arrivalText } });
		} else {
			await Orgs.updateAsync({ _id: org._id }, { $unset: { "valueOverrides.expressDriveUp.arrivalText": 1 } });
		}
		return true;
	},
	async updateDunningComsSettings(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.();
		let day = +options.day;
		let followUp = +options.followUp;
		let total = +options.total;
		let date = options.epochDate;
		const org = await Orgs.current();
		if (!currentPerson || currentPerson.type != "admin") {
			throw new Meteor.Error(403, "Access denied");
		}
		if (!total) {
			throw new Meteor.Error(403, "Total emails is a required field")
		}
		if (day < 1) {
			throw new Meteor.Error(403, "Send first email after ... days must be greater than 0")
		}
		if (total < 1) {
			throw new Meteor.Error(403, "Stop after ... days must be greater than 0")
		}
		if (!date) {
			throw new Meteor.Error(403, "Earliest invoice date field is required to save")
		}
		if (!followUp && total > 1) {
			throw new Meteor.Error(403, "Send follow-up emails every ... days must have a value greater than 1")
		}
		if (followUp == 0) {
			throw new Meteor.Error(403, "Send follow-up emails every ... days must have a value greater than 1")
		}

		await Orgs.updateAsync({ _id: org._id }, { $set: { "billing.dunningComs": options } })
	},
	async updateAdpFtpLoginCredentials(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.();
		if (!currentPerson || currentPerson.type !== "admin")
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();
		await Orgs.updateAsync({ _id: org._id }, { $set: { ftpCredentials: { adp: options } } });
	},
	async updateTimeConfirmationSettings(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.();
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const enabled = options.enabled;
		const confirmationDay = options.confirmationDay;

		const org = await Orgs.current();
		await Orgs.updateAsync({ _id: org._id }, { $set: { "customizations.people/timeConfirmation/enabled": enabled } });
		if (confirmationDay !== "none") {
			await Orgs.updateAsync({ _id: org._id }, { $set: { "valueOverrides.timeConfirmation.confirmationDay": confirmationDay } });
		} else {
			await Orgs.updateAsync({ _id: org._id }, { $unset: { "valueOverrides.timeConfirmation.confirmationDay": 1 } })
		}
		return true;
	},
	async updateAdpEmailSettings(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.();
		if (!currentPerson || currentPerson.type !== "admin")
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.current();
		await Orgs.updateAsync({ _id: org._id }, { $set: { adp: options } });
	},
	async insertDocumentRepositoryItem(options) {
		let currentUser = await Meteor.userAsync(),
			userPerson = await currentUser?.fetchPerson();
		await processPermissions({
			assertions: [{ context: "documents", action: "edit" }],
			evaluator: (person) => person.type == "admin" || person.type == "family",
			throwError: true
		});

		if (userPerson.type == "family" && !await Relationships.findOneAsync({ relationshipType: "family", targetId: options.personId, personId: userPerson._id }))
			throw new Meteor.Error(403, "Access denied for this relationship");

		const org = await Orgs.findOneAsync({ _id: currentUser.orgId }),
			documentDefinition = org.documentDefinitions && _.find(org.documentDefinitions, (dd) => { return dd._id == options.templateId; });

		if (!documentDefinition)
			throw new Meteor.Error(500, "No document definition found.");

		const repositoryKey = options.tokenId && (currentUser.orgId + "/" + currentUser._id + "/" + options.tokenId);
		const query = {};
		const setKey = `documentItems.${options.templateId}.`;
		if (options.action == "exempt") {
			await People.updateAsync({ _id: options.personId }, { "$addToSet": { "documentExemptions": options.templateId } });
			return;
		} else if (options.action == "assign") {
			await People.updateAsync({ _id: options.personId }, { "$addToSet": { "documentAssignments": options.templateId } });
			return;
		} else if (options.action == "unassign") {
			await People.updateAsync({ _id: options.personId }, { "$pull": { "documentAssignments": options.templateId } });
			return;
		} else if (options.action == "ack") {
			query[setKey + "templateOptionResult"] = {
				action: "ack",
				date: new Date().valueOf(),
				personId: userPerson._id,
				personName: `${userPerson.firstName} ${userPerson.lastName}`
			};
		} else {
			query[setKey + "createdAt"] = new Date().valueOf();
			query[setKey + "createdByPersonId"] = currentUser.personId;
			query[setKey + "createdByUserId"] = currentUser._id;
			query[setKey + "token"] = options.tokenId;
			query[setKey + "repositoryKey"] = repositoryKey;
		}

		await People.updateAsync({ _id: options.personId }, { "$set": query });
	},
	async updateDocumentRepositoryItem(options) {
		let currentUser = await Meteor.userAsync(),
			userPerson = await currentUser?.fetchPerson(),
			targetPerson = await People.findOneAsync({ orgId: currentUser.orgId, _id: options.personId });
		await processPermissions({
			assertions: [{ context: "documents", action: "edit" }],
			evaluator: (person) => person.type == "admin",
			throwError: true
		});
		if (!currentUser || !userPerson || !targetPerson)
			throw new Meteor.Error(403, "Access denied");

		const org = await Orgs.findOneAsync({ _id: currentUser.orgId }),
			documentDefinition = org.documentDefinitions && _.find(org.documentDefinitions, (dd) => { return dd._id == options.documentId; }),
			targetDoc = targetPerson.documentItems && targetPerson.documentItems[options.documentId],
			setKey = "documentItems." + options.documentId + ".";

		if (!documentDefinition) throw new Meteor.Error(500, "No document definition found.");
		if (!targetDoc) throw new Meteor.Error(500, "No document found.");

		const query = {};
		if (options.action == "approve") {
			query[setKey + "approvedByPersonId"] = userPerson._id;
			query[setKey + "approvedByUserId"] = currentUser._id;
			query[setKey + "approvedAt"] = new Date().valueOf();
		} else if (options.action == "reject") {
			query[setKey + "rejectedByPersonId"] = userPerson._id;
			query[setKey + "rejectedByUserId"] = currentUser._id;
			query[setKey + "rejectedAt"] = new Date().valueOf();
			if (options.rejectionNotes) query[setKey + "rejectionNotes"] = options.rejectionNotes
			if (Meteor.isServer && options.notifyPerson) {
				const createdByPerson = targetDoc.createdByPersonId && await People.findOneAsync(targetDoc.createdByPersonId);
				Meteor.defer(async function () {
					await processDocumentNotification({ type: "reject", documentDefinition, targetPerson: createdByPerson, additionalNotes: options.rejectionNotes });
				});
			}
		} else if (options.action == "archive") {
			query[setKey + "archivedByPersonId"] = userPerson._id;
			query[setKey + "archivedByUserId"] = currentUser._id;
			query[setKey + "archivedAt"] = new Date().valueOf();
		}
		if (!_.isEmpty(query)) {
			await People.updateAsync({ _id: targetPerson._id }, { "$set": query });
		}
	},
	async insertPeopleDataValidation(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const audienceType = (options.audience == "family") ? "family" : { $in: ["admin", "staff"] }
		const audienceQuery = { orgId: currentPerson.orgId, type: audienceType, inActive: { $ne: true } };

		const peopleIds = await People.find(audienceQuery, { fields: { _id: 1 } }).mapAsync(p => p._id)
		const org = await Orgs.current();
		let childReportProfileFields = null;
		if (options.childReportProfileFields == true && org && org.valueOverrides && org.valueOverrides.reportChildDataValidationFields) {
			childReportProfileFields = org.valueOverrides.reportChildDataValidationFields;
		}

		const pdvId = await PeopleDataValidations.insertAsync({
			title: options.title,
			description: options.description,
			audience: options.audience,
			peopleIds,
			responses: [],
			ongoing: options.ongoing ? true : false,
			orgId: currentPerson.orgId,
			profileFields: options.profileFields,
			reportProfileFields: options.reportProfileFields,
			active: true,
			childReportProfileFields,
			createdAt: new Date().valueOf(),
			requiredFields: options?.requiredFields ?? [],
		});
		return pdvId;
	},
	async updatePeopleDataValidation(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await PeopleDataValidations.updateAsync({ _id: options._id, orgId: currentPerson.orgId }, { $set: { description: options.description, title: options.title } })
	},
	async clonePeopleDataValidation(id) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const pdv = await PeopleDataValidations.findOneAsync(id);
		if (!pdv || pdv.orgId != currentPerson.orgId) {
			throw new Meteor.Error(403, "Access denied");
		}

		const audienceType = (pdv.audience == "family") ? "family" : { $in: ["admin", "staff"] }
		const audienceQuery = { orgId: currentPerson.orgId, type: audienceType, inActive: { $ne: true } };

		const peopleIds = await People.find(audienceQuery, { fields: { _id: 1 } }).mapAsync(p => p._id)

		const pdvId = await PeopleDataValidations.insertAsync({
			title: pdv.title,
			description: pdv.description,
			orgId: currentPerson.orgId,
			profileFields: pdv.profileFields,
			active: true,
			responses: [],
			peopleIds,
			audience: pdv.audience,
			ongoing: pdv.ongoing ? true : false,
			reportProfileFields: pdv.reportProfileFields || null,
			childReportProfileFields: pdv.childReportProfileFields || null,
			createdAt: new Date().valueOf(),
		});

		return pdvId;
	},
	async sendPeopleDataValidationReminder(id) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		const pdv = await PeopleDataValidations.findOneAsync(id);
		if (!pdv || pdv.orgId != currentPerson.orgId) {
			throw new Meteor.Error(403, "Access denied");
		}

		if (Meteor.isServer) { Meteor.defer(async function () { await processDataValidationReminder(id); }) };
	},
	async sendReportFile({ dataReport, email }) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");
		if (Meteor.isServer) { Meteor.defer(async function () { await factoryReportEmail({ dataReport, email, orgId: currentPerson.orgId, SSR, Blaze }); }) };
	},
	async stopPeopleDataValidation(_id) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await PeopleDataValidations.updateAsync({ _id, orgId: currentPerson.orgId }, { $set: { active: false } });
	},
	async deletePeopleDataValidation(_id) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || currentPerson.type != "admin")
			throw new Meteor.Error(403, "Access denied");

		await PeopleDataValidations.removeAsync({ _id, orgId: currentPerson.orgId });
	},
	async insertCurriculumTheme(options) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		let currentUser = await Meteor.userAsync(),
			userPerson = await currentUser?.fetchPerson();

		if (!options.name) throw new Meteor.Error(500, "Name cannot be blank.");

		const themeId = await CurriculumThemes.insertAsync({
			name: options.name,
			description: options.description,
			orgId: currentUser.orgId,
			selectedGroups: options.selectedGroups,
			createdByPersonId: userPerson._id,
			createdAt: new Date().valueOf()
		});
		return themeId;
	},
	async updateCurriculumTheme(options) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		let currentUser = await Meteor.userAsync(),
			userPerson = await currentUser?.fetchPerson();

		const findQuery = { orgId: currentUser.orgId, _id: options.themeId }, updateQuery = {}, setQuery = {};
		if (options.addDays) {

			const adjustedDays = await Promise.all(options.addDays.map(async ad => DateTimeUtils.getDatestampInTimezone(ad, (await Orgs.current()).getTimezone())));
			updateQuery["$addToSet"] = { "selectedDays": { "$each": adjustedDays } };
		}
		if (options.selectedGroups)
			setQuery["selectedGroups"] = options.selectedGroups;
		if (options.name)
			setQuery["name"] = options.name;
		if (options.description)
			setQuery["description"] = options.description;
		if (options.removeDay) {
			const adjustedDay = DateTimeUtils.getDatestampInTimezone(options.removeDay, (await Orgs.current()).getTimezone());
			const activitiesOnDay = await Curriculums.findOneAsync({ orgId: userPerson.orgId, curriculumThemeId: options.themeId, scheduledDate: adjustedDay });

			if (activitiesOnDay)
				throw new Meteor.Error(500, "Please remove all activities from the day before removing the day.");

			updateQuery["$pull"] = { "selectedDays": options.removeDay };
		}

		if (!_.isEmpty(setQuery))
			updateQuery["$set"] = setQuery;

		if (!_.isEmpty(updateQuery)) {
			await CurriculumThemes.updateAsync(findQuery, updateQuery);
			if (options.selectedGroups)
				await Curriculums.updateAsync({ orgId: userPerson.orgId, curriculumThemeId: options.themeId }, { "$set": { "selectedGroups": options.selectedGroups } }, { "multi": true });
		}
	},

	async deleteCurriculumTheme(options) {
		processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type === "admin" || thisPerson.type === "staff",
			throwError: true
		});

		let theme = await CurriculumThemes.findOneAsync({ _id: options.themeId });
		theme = new CurriculumTheme(theme);

		if (!theme) {
			throw new Meteor.Error(500, "Theme not found");
		}

		const activities = await Curriculums.find({ curriculumThemeId: options.themeId }).fetchAsync();
		if (activities.length > 0) {
			throw new Meteor.Error(500, "All activities must be removed from theme before it can be deleted.");
		}

		await CurriculumThemes.removeAsync({ _id: options.themeId });
	},
	async archiveCurriculum(curriculumId) {
		await processPermissions({
			assertions: [{ context: "activities", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin",
			throwError: true
		});
		let currentUser = await Meteor.userAsync(),
			userPerson = await currentUser?.fetchPerson();

		await Curriculums.updateAsync({ orgId: userPerson.orgId, _id: curriculumId }, { "$set": { archived: true } });
	},
	async dismissCallout(options) {
		let currentUser = await Meteor.userAsync(),
			userPerson = await currentUser?.fetchPerson();

		if (!currentUser || !userPerson)
			throw new Meteor.Error(403, "Access denied");

		await People.updateAsync({ _id: userPerson._id }, { $pull: { callouts: { _id: options.id } } });
	},
	async generatePortfolio(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || !_.contains(["admin", "staff"], currentPerson.type))
			throw new Meteor.Error(403, "Access denied");


		const portfolioMoments = await Moments.find({ orgId: currentPerson.orgId, momentType: "portfolio", taggedPeople: options.personId, sortStamp: { "$gte": options.startDate, "$lt": options.endDate } }).fetchAsync();
		/*
				portfolioMoments.forEach( (pm) => {
					const curriculum = Curriculums.findOne({orgId: currentPerson.orgId, _id: pm.portfolioCurriculumId});
					if (curriculum) pm.curriculum = curriculum;
				}); */
		const currentOrg = await Orgs.current();
		const targetPerson = await People.findOneAsync({ orgId: (await Meteor.userAsync())?.orgId, _id: options.personId });
		const allStandards = await Curriculums.getStandards(),
			portfolioSections = _.chain(portfolioMoments)
				.map((pm) => {
					pm.portfolioAssessments = pm.portfolioAssessments || [];
					const filteredAssessments = [];
					pm.portfolioAssessments.forEach((pa) => {
						pa.attachedMedia = pm.attachedMedia();
						//protect against bad standardId
						const matchedStandard = _.find(allStandards, (s) => s.standardId == pa.standardId),
							matchedAssessmentLevel = _.find(currentOrg.getAvailableAssessmentLevels(), (l) => l.value == pa.value);
						if (pa.standardId && pa.standardId.length > 0 && matchedStandard) {
							pa.matchedStandard = matchedStandard;
							pa.matchedAssessmentLevel = matchedAssessmentLevel;
							pa.createdAt = pm.sortStamp;
							const matchedOverride = targetPerson?.assessmentOverrides?.[pa.standardId];
							if (matchedOverride) {
								//pa.value = matchedOverride.level;
								pa.overrideData = matchedOverride;
							}
							filteredAssessments.push(pa);
						}
					});
					return filteredAssessments;
				})
				.flatten()
				.groupBy((pa) => pa.matchedStandard.category || "All")
				.map((categoryAssessments, category) => {
					const groupedStandardAssessments = _.groupBy(categoryAssessments, (ca) => ca.standardId);
					const outputStandards = _.map(groupedStandardAssessments, (standardAssessments, currentStandardId) => {
						const lastAssessment = _.sortBy(standardAssessments, sa => -1 * sa.createdAt)[0]["value"],
							maxAssessment = _.max(_.pluck(standardAssessments, "value")),
							override = _.first(standardAssessments).overrideData;
						let useValue = maxAssessment;
						if (lastAssessment && currentOrg.hasCustomization("modules/curriculum/useLastAssessment")) {
							useValue = lastAssessment;
						}
						if (override) {
							useValue = override.level;
						}
						return {
							lastAssessment,
							maxAssessment,
							avgAssessment: _.reduce(standardAssessments, (memo, sa) => memo + sa.value, 0) / standardAssessments.length,
							assessmentCount: standardAssessments.length,
							standard: _.first(standardAssessments).matchedStandard,
							override,
							useValue
						}
					});

					const categoryMedia = _.chain(categoryAssessments)
						.pluck("attachedMedia")
						.flatten()
						.map((am) => am.getTimelinePhoto)
						.uniq()
						.value();
					return {
						category,
						categoryMedia,
						outputStandards
					}
				})
				.sortBy((category) => category.category)
				.value();

		if (options.ageGroup) {

			const allAgeGroupStandards = _.filter(allStandards, s => s.ageGroup == options.ageGroup);
			_.each(allAgeGroupStandards, standard => {

				if (!_.find(portfolioSections, ps => ps.category == standard.category)) {
					portfolioSections.push({ category: standard.category, categoryMedia: [], outputStandards: [] });
				}
				const matchedSection = _.find(portfolioSections, ps => ps.category == standard.category);
				if (!_.find(matchedSection.outputStandards, os => os.standard.standardId == standard.standardId)) {
					const matchedOverride = targetPerson?.assessmentOverrides?.[standard.standardId];
					let useValue = 0;
					if (matchedOverride) {
						useValue = matchedOverride.level;
					}
					matchedSection.outputStandards.push({ maxAssessment: 0, avgAssessment: 0, assessmentCount: 0, useValue, standard: standard, override: matchedOverride });
				}

			});
		}

		const highlightsCategory = {
			category: "Highlights",
			categoryMedia: _.reduce(portfolioSections, (memo, ps) => { return memo.concat(ps.categoryMedia); }, []),
			"outputStandards": _.map(portfolioSections, ps => {
				return {
					"maxAssessment": _.max(_.pluck(ps.outputStandards, "maxAssessment")),
					"avgAssessment": Math.floor(_.reduce(ps.outputStandards, (memo, a) => memo + a.avgAssessment, 0) /
						_.reduce(ps.outputStandards, (memo, a) => memo + a.assessmentCount, 0)),
					"assessmentCount": _.reduce(ps.outputStandards, (memo, a) => memo + a.assessmentCount, 0),
					"useValue": _.max(_.pluck(ps.outputStandards, "useValue")),
					"standard": {
						"ageGroup": null,
						"category": ps.category,
						"benchmark": ps.category,
						"standardId": null,
						"source": null
					},
					"isHighlight": true
				};
			})
		};

		portfolioSections.unshift(highlightsCategory);

		return portfolioSections;
	},
	async sleepCheck(options) {
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (!currentPerson || !_.contains(["admin", "staff"], currentPerson.type))
			throw new Meteor.Error(403, "Access denied");

		const matchedPeople = await People.find({ orgId: currentPerson.orgId, _id: { $in: options.selectedPeople }, type: "person" }).fetchAsync(),
			matchedPeopleIds = matchedPeople.map((p) => p._id),
			todayStamp = new moment.tz((await Orgs.current()).getTimezone()).startOf('day').valueOf(),
			momentQuery = { orgId: currentPerson.orgId, momentType: "sleep", taggedPeople: { $in: matchedPeopleIds }, sortStamp: { "$gte": todayStamp }, endTime: { "$exists": false } },
			matchedMoments = await Moments.find(momentQuery).fetchAsync(),
			peopleLoggedIds = [];

		for (let matchedMoment of matchedMoments) {

			const currentMomentPeopleIds = matchedPeopleIds.filter((pid) => _.contains(matchedMoment.taggedPeople, pid));
			const createdAt = new Date().valueOf();
			const newSleepChecks = currentMomentPeopleIds.map((pid) => {
				peopleLoggedIds.push(pid);
				const sc = {
					personId: pid,
					createdAt,
					createdBy: currentPerson._id
				};
				if (options?.opts?.sleepPosition) {
					sc.sleepPosition = options.opts.sleepPosition
				}
				if (options?.opts?.distressedSleep) {
					sc.distressedSleep = options.opts.distressedSleep
				}
				return sc;
			});

			if (newSleepChecks.length > 0) await Moments.updateAsync({ _id: matchedMoment._id }, { $addToSet: { sleepChecks: { $each: newSleepChecks } }, $set: { sleepLastSleepCheck: createdAt } });
		}

		if (Meteor.isServer) {
			processAnalytics({
				metaCxData: {
					type: 'sleep-check', data: {
						orgId: (await Meteor.userAsync())["orgId"],
						orgName: (await Orgs.current()).name,
						personId: currentPerson._id,
						type: currentPerson.type,
						groupId: currentPerson.checkInGroupId
					}
				}
			});
		}

		return {
			successfulSleepChecks: matchedPeople.filter((p) => _.contains(peopleLoggedIds, p._id)).map((p) => ({ _id: p._id, firstName: p.firstName, lastName: p.lastName })),
			sleepNotFound: matchedPeople.filter((p) => !_.contains(peopleLoggedIds, p._id)).map((p) => ({ _id: p._id, firstName: p.firstName, lastName: p.lastName }))
		};
	},
	async updateGroupOrder(options) {
		await processPermissions({
			assertions: [{ context: "groups", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()

		if (options.groupIds) {
			for (const [idx, groupId] of options.groupIds.entries()) {
				await Groups.updateAsync({ _id: groupId, orgId: currentPerson.orgId }, { "$set": { sortOrder: idx } });
			};
		}
	},
	async setCheckInCheckOutQrCodesExpire(updatedValue) {
		await processPermissions({
			assertions: [{ context: "settings", action: "edit" }],
			evaluator: (thisPerson) => thisPerson.type == "admin" || thisPerson.type == "staff",
			throwError: true
		});
		const cuser = await Meteor.userAsync();
		const currentPerson = await cuser?.fetchPerson?.()
		if (updatedValue) {
			// Sets checkInCheckOutQrCodesExpireAfterMinutes directly on the org for now:
			// We store in minutes to allow more granularity, mainly for QA/testing purposes.
			const currentOrg = await Orgs.findOneAsync({ _id: currentPerson.orgId });
			if (currentOrg) {
				await Orgs.updateCheckInCheckOutQrCodesExpireAfterMinutes(currentOrg._id, updatedValue);
			}
		}
	},
	async setFamilyRegistrationSettings(options = {}) {
		await processPermissions({
			assertions: [{ context: 'registrationFlowQuestions', action: 'edit' }],
			evaluator: (person) => person.type === 'admin',
			throwError: true
		});
		const autoGroupSelection = options.autoGroupSelection || null;
		const requiredContactsCount = (options.requiredContactsCount || 0);
		if (requiredContactsCount < 0) {
			throw new Meteor.Error('500', 'Emergency Contacts/Additional Pickups cannot be less than 0');
		}

		const profileFields = (await People.getAllProfileFieldInputFields('person')).filter(f => f.type !== 'query');

		const profileFieldsMap = new Map();
		for (const field of profileFields) {
			profileFieldsMap.set(field.name, field);
		}

		const validateTypes = function (mappedField, questionType) {
			if (!mappedField.type) {
				return false;
			}
			switch (mappedField.type) {
				case 'text':
				case 'string':
					return questionType === 'text' || questionType === 'checkbox';
				case 'date':
					return questionType === 'date';
				case 'select':
					return questionType === 'selectSingle' ||
						(questionType === 'selectMultiple' && mappedField.multi);
				default:
					return false;
			}
		}

		const questions = [];
		const possibleTypes = [
			'text',
			'date',
			'selectSingle',
			'selectMultiple',
			'checkbox',
			'conditional'
		];
		const choiceTypes = ['selectSingle', 'selectMultiple'];
		const possibleProfileFields = profileFields.map(field => field.name);
		let index = 1;
		for (const question of (options.questions ?? [])) {
			if (!possibleTypes.includes(question.type)) {
				throw new Meteor.Error('500', 'Invalid type for question ' + index);
			}
			if (choiceTypes.includes(question.type) && !question.choices.length) {
				throw new Meteor.Error('500', 'Please fill in choices for question ' + index);
			}
			if ((question.question || '') === '' || question.question.trim() === '') {
				throw new Meteor.Error('500', 'Invalid question for question ' + index);
			}
			if (question.type === 'conditional') {
				// the "question" must have an ifYes and ifNo property:
				if (!question.ifYes) {
					throw new Meteor.Error('500', `Question ${index} is a conditional question, but does not have an "ifYes" property.`);
				}
				if (!question.ifNo) {
					throw new Meteor.Error('500', `Question ${index} is a conditional question, but does not have an "ifNo" property.`);
				}
				//
				// The "type" for conditional items is essentially a subtype of the parent question's type and also
				// includes "noFollowUpNeeded" which is a special case for conditional questions that don't have a follow up.
				//
				const possibleTypesWithoutConditional = [...possibleTypes.filter(t => t !== 'conditional'), 'noFollowUpNeeded']
				// Make sure question.ifYes.type is valid:
				if (!possibleTypesWithoutConditional.includes(question.ifYes.type)) {
					throw new Meteor.Error('500', `Question ${index}'s "If Yes" question has an invalid type.`);
				}
				// Make sure question.ifNo.type is valid:
				if (!possibleTypesWithoutConditional.includes(question.ifNo.type)) {
					throw new Meteor.Error('500', `Question ${index}'s "If No" question has an invalid type.`);
				}
				// Make sure question.ifYes.choices is valid:
				if (choiceTypes.includes(question.ifYes.type) && (!question.ifYes.choices || !question.ifYes.choices.length)) {
					throw new Meteor.Error('500', `Question ${index}'s "If Yes" question has no choices.`);
				}
				// Make sure question.ifNo.choices is valid:
				if (choiceTypes.includes(question.ifNo.type) && (!question.ifNo.choices || !question.ifNo.choices.length)) {
					throw new Meteor.Error('500', `Question ${index}'s "If No" question has no choices.`);
				}
				// If the question is a conditional question with a follow up, make sure the follow up is valid:
				if (question.ifYes.type !== 'noFollowUpNeeded') {
					// Make sure question.ifYes.question is valid:
					if ((question.ifYes.question || '') === '' || question.ifYes.question.trim() === '') {
						throw new Meteor.Error('500', `Question ${index}'s "If Yes" item has no question.`);
					}
					// Make sure question.ifYes.mappedTo is valid:
					if ((question.ifYes.mappedTo || '') === '' || !possibleProfileFields.includes(question.ifYes.mappedTo)) {
						throw new Meteor.Error('500', `Question ${index}'s "If Yes" item has an invalid mapping.`);
					}
					// Validate the type of the mapped field:
					if (!validateTypes(profileFieldsMap.get(question.ifYes.mappedTo) ?? {}, question.ifYes.type)) {
						throw new Meteor.Error('500', `Question ${index}'s "If Yes" item has an invalid mapping. The type of the mapped field does not match the question type.`);
					}
				}
				// If the question is a conditional question with a follow up, make sure the follow up is valid:
				if (question.ifNo.type !== 'noFollowUpNeeded') {
					// Make sure question.ifNo.question is valid:
					if ((question.ifNo.question || '') === '' || question.ifNo.question.trim() === '') {
						throw new Meteor.Error('500', `Question ${index}'s "If No" item has no question.`);
					}
					// Make sure question.ifNo.mappedTo is valid:
					if ((question.ifNo.mappedTo || '') === '' || !possibleProfileFields.includes(question.ifNo.mappedTo)) {
						throw new Meteor.Error('500', `Question ${index}'s "If No" item has an invalid mapping.`);
					}
					// Validate the type of the mapped field:
					if (!validateTypes(profileFieldsMap.get(question.ifNo.mappedTo) ?? {}, question.ifNo.type)) {
						throw new Meteor.Error('500', `Question ${index}'s "If No" item has an invalid mapping. The type of the mapped field does not match the question type.`);
					}
				}
			} else {
				if ((question.mappedTo || '') === '' || !possibleProfileFields.includes(question.mappedTo)) {
					throw new Meteor.Error('500', 'Invalid mapping for question ' + index);
				}
				if (!validateTypes(profileFieldsMap.get(question.mappedTo) ?? {}, question.type)) {
					throw new Meteor.Error('500', 'Invalid mapping for question ' + index + '. The type of the mapped field does not match the question type.');
				}
				if ((question.includedLink || '') !== '' && !MiscUtils.isValidHttpUrl(question.includedLink)) {
					throw new Meteor.Error('500', 'Invalid url for question ' + index);
				}
			}
			questions.push({
				type: question.type,
				question: question.question,
				mappedTo: question.mappedTo,
				includedLink: question.includedLink,
				isRequired: !!question.isRequired,
				choices: question.choices ?? [],
				ifYes: question.ifYes ?? null,
				ifNo: question.ifNo ?? null
			});
			++index;
		}

		const settings = {
			requiredContactsCount: requiredContactsCount,
			questions: questions,
		}

		if (autoGroupSelection) {
			settings.autoGroupSelection = autoGroupSelection;
		}

		await Orgs.updateAsync(
			{ _id: (await Orgs.current())._id },
			{ $set: { 'familyRegistrationSettings': settings } }
		);
	},
	async adjustInvoiceDueDate({ monthlyDay, weeklyDay, personId }) {
		if (1 > monthlyDay || monthlyDay > 31) {
			throw new Meteor.Error(500, "Day must be between 1 and 31.")
		}
		await People.updateAsync({ _id: personId }, {
			$set: {
				"billing.invoiceDateOverride": {
					monthlyPlanDueDay: monthlyDay,
					weeklyPlanDueDay: weeklyDay
				}
			}
		})

	},
	async removeInvoiceDueDate({ personId }) {
		await People.updateAsync({ _id: personId }, {
			$unset: {
				"billing.invoiceDateOverride": {
					monthlyPlanDueDay: '',
					weeklyPlanDueDay: ''
				}
			}
		})
	}
});

var tokenString = function () {
	var chars = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz";
	var string_length = 20;
	var randomstring = '';
	for (var i = 0; i < string_length; i++) {
		var rnum = Math.floor(Math.random() * chars.length);
		randomstring += chars.substring(rnum, rnum + 1);
	}
	return randomstring;
};

function roundToTwo(num) {
	return +(Math.round(num + "e+2") + "e-2");
}

async function checkDiscountCounts(allocations, org = null) {
	const currentOrg = org || await Orgs.current();
	const discountTypeCounts = _.countBy(allocations, (a) => {
		const matchedDiscount = a.allocationType == "discount" && a.discountType !== "bundle" && _.find(currentOrg.availableDiscountTypes(false, true), ad => ad.type == a.discountType);
		if (!matchedDiscount)
			return "none";
		if (matchedDiscount.overrideSingleDiscount)
			return "override";
		else return "single";
	});
	if (discountTypeCounts["single"] > 1 ||
		(discountTypeCounts["single"] == 1 && discountTypeCounts["override"] > 1) ||
		(discountTypeCounts["override"] > 2))
		throw new Meteor.Error(500, "You have attempted to add more discounts than currently allowed to this customer.");
}

async function enrollBillingPlan(options, orgId = null, updateCurrentPlan = false) {

	const currentUser = orgId ? null : await Meteor.userAsync();
	const org = await Orgs.findOneAsync({ _id: orgId || currentUser.orgId });
	const person = await People.findOneAsync({ orgId: org._id, _id: options.personId });
	if (!person || person.type != "person") throw new Meteor.Error(500, "Cannot associate billing plan with this type of person");

	const currentPlans = _.deep(person, "billing.enrolledPlans");

	if (!orgId && org.hasCustomization("billing/plans/allowSingleDiscount"))
		await checkDiscountCounts(options.allocations);

	const timezone = org.getTimezone();
	const createdAtDate = new moment.tz(timezone);
	if (options.incrementCreatedAt) {
		createdAtDate.add(options.incrementCreatedAt, "seconds");
	}

	const selectedPlan = _.find(org.billing.plansAndItems, (p) => { return p._id === options.plan });
	const newPlan = {
		_id: options.plan,
		planDetails: selectedPlan,
		enrollmentDate: new moment.tz(options.enrollment_date, "MM/DD/YYYY", timezone).valueOf(),
		allocations: options.allocations,
		createdAt: createdAtDate.valueOf()
	};
	MiscUtils.setDocumentId(newPlan, 'uid');

	// Set the amount based on the selected week if it's a selective week plan
	if (selectedPlan.details?.selectiveWeeks?.length && selectedPlan.details?.selectiveWeekAmounts?.length) {
		const start = options.enrollment_date;
		const end = options.expiration_date;
		const matchedWeek = selectedPlan.details.selectiveWeeks.find((week) => week[0] === start && week[1] === end);
		if (matchedWeek) {
			const index = selectedPlan.details.selectiveWeeks.indexOf(matchedWeek);
			if (index > -1) {
				newPlan.planDetails.amount = selectedPlan.details.selectiveWeekAmounts[index] || selectedPlan.amount;
			}
		}
	}

	if (options.expiration_date) {
		newPlan.expirationDate = new moment.tz(options.expiration_date, "MM/DD/YYYY", timezone).valueOf();
	}

	if (options.override_rate) {
		newPlan.overrideRate = roundToTwo(parseFloat(options.override_rate));
	}

	if (options.reservationId) {
		newPlan.reservationId = options.reservationId;
		newPlan.enrollmentForecastStartDate = newPlan.enrollmentDate;
		if (newPlan.expiration_date) {
			newPlan.enrollmentForecastEndDate = newPlan.expiration_date;
		}
	} else {
		if (options.enrollment_forecast_start) {
			newPlan.enrollmentForecastStartDate = new moment.tz(options.enrollment_forecast_start, "MM/DD/YYYY", timezone).valueOf();
		}

		if (options.enrollment_forecast_end) {
			newPlan.enrollmentForecastEndDate = new moment.tz(options.enrollment_forecast_end, "MM/DD/YYYY", timezone).valueOf();
		}
	}
	if (options.bundlePlanId) {
		newPlan.bundlePlanId = options.bundlePlanId;
	}

	const existingPlan = currentPlans?.find(p => p._id === newPlan._id && p.createdAt === newPlan.createdAt)
	if (updateCurrentPlan && existingPlan) {
		// Make sure createdAt of a child do not get updated, so I'm overridding with the existing data
		newPlan.createdAt = existingPlan.createdAt;
		await People.updateAsync({ _id: person._id, "billing.enrolledPlans._id": newPlan._id, "billing.enrolledPlans.createdAt": newPlan.createdAt }, { $set: { "billing.enrolledPlans.$": newPlan } });
	} else {
		await People.updateAsync({ _id: person._id }, { $push: { "billing.enrolledPlans": newPlan } });
	}
	await Orgs.updateAsync({ _id: org._id }, { $inc: { "billing.stats.totalEnrollmentCount": 1 } });
}

/**
 * Recalculates the bundle discount for a person's enrolled plans based on the provided bundlePlanId.
 *
 * @param {object} person - The person object for whom to recalculate the bundle discount.
 * @param {string} bundlePlanId - The identifier of the bundle plan.
 *
 * @throws {Meteor.Error} Throws an error if person or bundlePlanId is missing.
 */
export async function recalculateBundleDiscount(person, bundlePlanId) {
	if (!person || !bundlePlanId) {
		throw new Meteor.Error("Cannot recalculate bundle discount without a person and bundle plan id");
	}

	const currentPlans = _.deep(person, 'billing.enrolledPlans');
	const bundlePlans = _.filter(currentPlans, ep => ep.bundlePlanId === bundlePlanId);
	let query = {};

	const removeBundle = async (selectedPerson, bundleId, bundlePair, removeAll = false) => {
		const query = {
			$unset: {
				'billing.enrolledPlans.$.bundlePlanId': 1
			},
			$pull: {
				'billing.enrolledPlans.$.allocations': { discountType: DiscountTypes.BUNDLE }
			}
		};

		if (removeAll || !bundlePlans) {
			await People.updateAsync({
				_id: selectedPerson._id,
				'billing.enrolledPlans.bundlePlanId': bundleId
			}, query, { multi: true });
		} else {
			for (const plan of bundlePair) {
				// since its possible to have two plans of the same _id, we delineate by createdAt
				await People.updateAsync(
					{ _id: selectedPerson._id, 'billing.enrolledPlans.createdAt': plan.createdAt },
					query,
					{ multi: true }
				);
			}
		}
	};


	const calculateDiscount = (plan1, plan2, days1Count, days2Count) => {
		const normalAmount = plan1.scaledAmounts[days1Count - 1] + plan2.scaledAmounts[days2Count - 1];
		const bundledAmount = bundle.scaledAmounts[days1Count - 1][days2Count - 1];
		const difference = normalAmount - bundledAmount;

		return difference / 2;
	};

	if (bundlePlans.length < 2) {
		// The bundle is not valid; remove the discounts
		removeBundle(person, bundlePlanId, null, true);
		return;
	}

	// Recalculate the bundle discount based on the number of days in the plans
	const org = await Orgs.findOneAsync({ _id: person.orgId });
	const bundle = _.find(org.availableBundles(true), b => b._id === bundlePlanId);

	if (!bundle) {
		removeBundle(person, bundlePlanId, null, true);
		return;
	}

	const completedBundlePairs = bundlePlans.length === 2 ? [bundlePlans] : MiscUtils.chunkArray(bundlePlans, 2);
	for (const bundlePair of completedBundlePairs) {
		const [ep1, ep2] = bundlePair;

		if (!ep1 || !ep2) {
			removeBundle(person, bundlePlanId, bundlePair);
			return;
		}

		const ep1Schedule = await Reservations.findOneAsync({ _id: ep1.reservationId });
		const ep2Schedule = await Reservations.findOneAsync({ _id: ep2.reservationId });

		if (!ep1Schedule || !ep2Schedule) {
			removeBundle(person, bundlePlanId, bundlePair);
			return;
		}

		let days1Count = ep1Schedule.recurringDays?.length || 5;
		let days2Count = ep2Schedule.recurringDays?.length || 5;

		const plan1 = _.find(org.billing?.plansAndItems ?? [], p => p._id === ep1._id);
		const plan2 = _.find(org.billing?.plansAndItems ?? [], p => p._id === ep2._id);

		if (!plan1 || !plan2) {
			removeBundle(person, bundlePlanId, bundlePair);
			return;
		}

		const discountAmount = calculateDiscount(plan1, plan2, days1Count, days2Count);

		const updatedCurrentPlans = currentPlans.map(p => {
			if ([ep1._id, ep2._id].includes(p._id) && [ep1.createdAt, ep2.createdAt].includes(p.createdAt)) {
				p.allocations = _.map(p.allocations, a => {
					if (a.discountType === DiscountTypes.BUNDLE) {
						a.amount = discountAmount;
					}
					return a;
				});
			}
			return p;
		});

		await People.updateAsync({ _id: person._id }, { $set: { 'billing.enrolledPlans': updatedCurrentPlans } });
	}
}


async function createAllRelationships(personId, targetId, orgId, primaryCaregiver, relationshipType, relationshipDescription, fromPlr) {
	let inserted = false;
	if (Array.isArray(relationshipType)) {
		for (const rt of relationshipType) {
			const newRelationship = {
				personId,
				targetId,
				relationshipType: rt,
				orgId,
				relationshipDescription,
				primaryCaregiver: (rt === 'family') ? primaryCaregiver : false,
				createdAt: new Date().valueOf()
			};

			await Relationships.direct.insertAsync(newRelationship);
			inserted = true;
		}
	} else {
		const newRelationship = {
			personId,
			targetId,
			relationshipType,
			orgId,
			relationshipDescription,
			primaryCaregiver: (relationshipType === 'family') ? primaryCaregiver : false,
			createdAt: new moment.tz().valueOf()
		};

		await Relationships.direct.insertAsync(newRelationship);
		inserted = true;
	}
	if (inserted && !fromPlr) {
		Meteor.defer(async function () {
			await Meteor.callAsync('sendFamilyToCrm', targetId);
			await Meteor.callAsync('setAutoPin', personId);
		});
	}
}

async function getPersonHubspotAndAirslateIds(person) {
	let response = null;
	if (person.type === 'person') {
		const guardianRelationships = await person.findOwnedRelationships({ onlyPrimaryCaregivers: true, onlyActive: true, noDuplicates: true });
		const guardians = [];
		for (const relationship of guardianRelationships) {
			const guardian = await People.findOneAsync({ _id: relationship.personId });
			guardians.push(guardian);
		}
		for (const guardian of guardians) {
			if (guardian.hubspotId) {
				response = { hubspotId: guardian.hubspotId };
				if (person.hubspotId) {
					response.hubspotId = person.hubspotId;
				}
				if (person.importIndex || parseInt(person.importIndex) === 0) {
					response.importIndex = parseInt(person.importIndex);
					break;
				}

				const childrenIds = _.pluck(guardian.findInheritedRelationships().fetch(), 'targetId');
				const children = await People.find({ _id: { $in: childrenIds }, inActive: { $ne: true } }).fetchAsync();

				response.importIndex = 0;
				children.forEach(child => {
					if (child._id === person._id || (!child.importIndex && parseInt(child.importIndex) !== 0)) {
						return;
					}
					if (response.importIndex <= parseInt(child.importIndex)) {
						response.importIndex = parseInt(child.importIndex) + 1;
					}
				});
			}
		}
	}
	return response;
}
